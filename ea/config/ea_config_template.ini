# Copy Trading EA Configuration Template
# Copy this file and rename to ea_config.ini
# Update with your actual values

[API]
# Backend API Configuration
base_url=https://api.copytrading.com
api_key=your-api-key-here
enable_ssl=true
timeout=5000
retry_attempts=3
retry_delay=1000

[PROVIDER]
# Provider EA Settings (for signal generators)
provider_id=your-provider-id
auto_send_signals=true
min_profit_points=10
min_volume=0.01
send_pending_orders=false
send_modifications=true
max_daily_signals=50
max_signal_volume=10.0
validate_signals=true

[FOLLOWER]
# Follower EA Settings (for signal receivers)
follower_id=your-follower-id
allowed_providers=provider1,provider2,provider3
auto_copy_enabled=true
copy_ratio=1.0
max_open_positions=10
min_free_margin=100.0

[RISK_MANAGEMENT]
# Risk Management Settings
risk_percent=2.0
max_lot_size=1.0
min_lot_size=0.01
max_daily_loss=500.0
max_drawdown=20.0
use_stop_loss=true
use_take_profit=true
max_risk_per_trade=5.0

[SYSTEM]
# System Settings
heartbeat_interval=30
enable_logging=true
log_level=INFO
log_file_max_size=10485760
log_backup_count=5
enable_performance_monitoring=true

[CONNECTION]
# Connection Settings
poll_interval=1
reconnect_interval=5
max_connection_attempts=10
connection_timeout=5000
keep_alive_interval=30

[TRADING]
# Trading Settings
slippage=20
magic_number=123456
trade_comment=Copy Trade
max_spread=50
check_market_hours=true
avoid_news_trading=false

[NOTIFICATIONS]
# Notification Settings
enable_alerts=true
enable_email_notifications=false
email_on_error=true
email_on_large_loss=true
large_loss_threshold=100.0

[ADVANCED]
# Advanced Settings
enable_debug_mode=false
save_signal_history=true
signal_history_days=30
enable_statistics=true
statistics_update_interval=300
enable_backup=true
backup_interval=3600

# Environment-specific overrides
[DEVELOPMENT]
# Development environment settings
log_level=DEBUG
enable_debug_mode=true
heartbeat_interval=10
poll_interval=2

[PRODUCTION]
# Production environment settings
log_level=INFO
enable_debug_mode=false
heartbeat_interval=30
poll_interval=1
enable_performance_monitoring=true

[TESTING]
# Testing environment settings
log_level=DEBUG
auto_copy_enabled=false
auto_send_signals=false
enable_debug_mode=true
