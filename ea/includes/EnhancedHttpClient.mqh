//+------------------------------------------------------------------+
//| EnhancedHttpClient.mqh                                           |
//| Enhanced HTTP client with connection pooling and smart polling  |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"

#include "Logger.mqh"

//--- Connection pool structure
struct ConnectionInfo {
    string url;
    datetime lastUsed;
    bool inUse;
    int connectionId;
    int failureCount;
};

//--- Request statistics
struct RequestStats {
    int totalRequests;
    int successfulRequests;
    int failedRequests;
    double avgResponseTime;
    datetime lastStatsReset;
};

//--- Polling configuration
struct PollingConfig {
    int baseInterval;        // Base polling interval in seconds
    int maxInterval;         // Maximum polling interval in seconds
    int backoffMultiplier;   // Backoff multiplier for failures
    int maxConsecutiveErrors;// Max consecutive errors before circuit breaker
    bool adaptivePolling;    // Enable adaptive polling based on activity
};

//+------------------------------------------------------------------+
//| Enhanced HTTP Client Class                                       |
//+------------------------------------------------------------------+
class CEnhancedHttpClient {
private:
    string          m_baseUrl;
    string          m_apiKey;
    bool            m_enableSsl;
    int             m_timeout;
    string          m_userAgent;
    CLogger*        m_logger;
    
    // Connection pooling
    ConnectionInfo  m_connectionPool[];
    int             m_maxConnections;
    int             m_connectionTimeout;
    
    // Request statistics and monitoring
    RequestStats    m_stats;
    
    // Polling configuration
    PollingConfig   m_pollingConfig;
    int             m_currentInterval;
    int             m_consecutiveErrors;
    bool            m_circuitBreakerOpen;
    datetime        m_lastSuccessfulRequest;
    
    // Request queue for offline scenarios
    string          m_queuedRequests[];
    int             m_maxQueueSize;

public:
    //--- Constructor
    CEnhancedHttpClient(string baseUrl, string apiKey, CLogger* logger = NULL, 
                       bool enableSsl = true, int timeout = 5000);
    
    //--- Destructor
    ~CEnhancedHttpClient();
    
    //--- HTTP Methods with enhanced error handling
    bool Get(string endpoint, string &response, int &statusCode);
    bool Post(string endpoint, string data, string &response, int &statusCode);
    bool Put(string endpoint, string data, string &response, int &statusCode);
    bool Delete(string endpoint, string &response, int &statusCode);
    
    //--- Enhanced polling methods
    bool StartPolling(string endpoint, int intervalSeconds = 1);
    void StopPolling();
    bool PollOnce(string endpoint, string &response);
    
    //--- Connection management
    bool TestConnection();
    void ResetConnectionPool();
    bool IsHealthy();
    
    //--- Configuration
    void SetPollingConfig(int baseInterval, int maxInterval, int backoffMultiplier, 
                         int maxErrors, bool adaptive = true);
    void SetTimeout(int timeout) { m_timeout = timeout; }
    void SetUserAgent(string userAgent) { m_userAgent = userAgent; }
    void SetMaxQueueSize(int size) { m_maxQueueSize = size; }
    
    //--- Statistics and monitoring
    RequestStats GetStats() { return m_stats; }
    void ResetStats();
    string GetHealthReport();
    
    //--- Queue management
    void QueueRequest(string method, string endpoint, string data = "");
    bool ProcessQueuedRequests();
    int GetQueueSize() { return ArraySize(m_queuedRequests); }

private:
    //--- Internal HTTP methods
    bool SendRequestWithRetry(string method, string url, string headers, 
                             string data, string &response, int &statusCode, int maxRetries = 3);
    bool SendSingleRequest(string method, string url, string headers, 
                          string data, string &response, int &statusCode);
    
    //--- Connection pool management
    int GetAvailableConnection(string url);
    void ReleaseConnection(int connectionId);
    void CleanupConnections();
    
    //--- Adaptive polling logic
    void UpdatePollingInterval(bool success);
    bool ShouldSkipPoll();
    
    //--- Helper methods
    string BuildUrl(string endpoint);
    string BuildHeaders(string contentType = "application/json");
    string UrlEncode(string str);
    bool IsRetryableError(int errorCode);
    string GetErrorDescription(int errorCode);
    
    //--- Circuit breaker logic
    void UpdateCircuitBreaker(bool success);
    bool IsCircuitBreakerOpen();
    
    //--- Statistics tracking
    void UpdateStats(bool success, double responseTime);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CEnhancedHttpClient::CEnhancedHttpClient(string baseUrl, string apiKey, CLogger* logger = NULL, 
                                        bool enableSsl = true, int timeout = 5000) {
    m_baseUrl = baseUrl;
    m_apiKey = apiKey;
    m_logger = logger;
    m_enableSsl = enableSsl;
    m_timeout = timeout;
    m_userAgent = "MT5-CopyTrade-Enhanced/2.0";
    
    // Remove trailing slash from base URL
    if(StringSubstr(m_baseUrl, StringLen(m_baseUrl) - 1) == "/") {
        m_baseUrl = StringSubstr(m_baseUrl, 0, StringLen(m_baseUrl) - 1);
    }
    
    // Initialize connection pool
    m_maxConnections = 5;
    m_connectionTimeout = 300; // 5 minutes
    ArrayResize(m_connectionPool, m_maxConnections);
    
    // Initialize polling configuration
    m_pollingConfig.baseInterval = 1;
    m_pollingConfig.maxInterval = 30;
    m_pollingConfig.backoffMultiplier = 2;
    m_pollingConfig.maxConsecutiveErrors = 5;
    m_pollingConfig.adaptivePolling = true;
    
    m_currentInterval = m_pollingConfig.baseInterval;
    m_consecutiveErrors = 0;
    m_circuitBreakerOpen = false;
    m_lastSuccessfulRequest = TimeCurrent();
    
    // Initialize request queue
    m_maxQueueSize = 100;
    ArrayResize(m_queuedRequests, 0);
    
    // Initialize statistics
    ResetStats();
    
    if(m_logger != NULL) {
        m_logger.Info("Enhanced HTTP Client initialized with base URL: " + m_baseUrl);
    }
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CEnhancedHttpClient::~CEnhancedHttpClient() {
    StopPolling();
    ResetConnectionPool();
    
    if(m_logger != NULL) {
        m_logger.Info("Enhanced HTTP Client destroyed");
    }
}

//+------------------------------------------------------------------+
//| Enhanced GET request with connection pooling                    |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::Get(string endpoint, string &response, int &statusCode) {
    if(IsCircuitBreakerOpen()) {
        if(m_logger != NULL) {
            m_logger.Warning("Circuit breaker is open, skipping request");
        }
        statusCode = -1;
        return false;
    }
    
    string url = BuildUrl(endpoint);
    string headers = BuildHeaders();
    
    datetime startTime = GetMicrosecondCount();
    bool result = SendRequestWithRetry("GET", url, headers, "", response, statusCode);
    double responseTime = (GetMicrosecondCount() - startTime) / 1000.0; // Convert to milliseconds
    
    UpdateStats(result && statusCode >= 200 && statusCode < 300, responseTime);
    UpdateCircuitBreaker(result && statusCode >= 200 && statusCode < 300);
    
    return result;
}

//+------------------------------------------------------------------+
//| Enhanced POST request                                            |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::Post(string endpoint, string data, string &response, int &statusCode) {
    if(IsCircuitBreakerOpen()) {
        // Queue the request for later processing
        QueueRequest("POST", endpoint, data);
        statusCode = -1;
        return false;
    }

    string url = BuildUrl(endpoint);
    string headers = BuildHeaders("application/json");

    datetime startTime = GetMicrosecondCount();
    bool result = SendRequestWithRetry("POST", url, headers, data, response, statusCode);
    double responseTime = (GetMicrosecondCount() - startTime) / 1000.0;

    UpdateStats(result && statusCode >= 200 && statusCode < 300, responseTime);
    UpdateCircuitBreaker(result && statusCode >= 200 && statusCode < 300);

    return result;
}

//+------------------------------------------------------------------+
//| Enhanced PUT request                                             |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::Put(string endpoint, string data, string &response, int &statusCode) {
    if(IsCircuitBreakerOpen()) {
        QueueRequest("PUT", endpoint, data);
        statusCode = -1;
        return false;
    }

    string url = BuildUrl(endpoint);
    string headers = BuildHeaders("application/json");

    datetime startTime = GetMicrosecondCount();
    bool result = SendRequestWithRetry("PUT", url, headers, data, response, statusCode);
    double responseTime = (GetMicrosecondCount() - startTime) / 1000.0;

    UpdateStats(result && statusCode >= 200 && statusCode < 300, responseTime);
    UpdateCircuitBreaker(result && statusCode >= 200 && statusCode < 300);

    return result;
}

//+------------------------------------------------------------------+
//| Enhanced DELETE request                                          |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::Delete(string endpoint, string &response, int &statusCode) {
    if(IsCircuitBreakerOpen()) {
        statusCode = -1;
        return false;
    }

    string url = BuildUrl(endpoint);
    string headers = BuildHeaders();

    datetime startTime = GetMicrosecondCount();
    bool result = SendRequestWithRetry("DELETE", url, headers, "", response, statusCode);
    double responseTime = (GetMicrosecondCount() - startTime) / 1000.0;

    UpdateStats(result && statusCode >= 200 && statusCode < 300, responseTime);
    UpdateCircuitBreaker(result && statusCode >= 200 && statusCode < 300);

    return result;
}

//+------------------------------------------------------------------+
//| Smart polling with adaptive intervals                           |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::PollOnce(string endpoint, string &response) {
    if(ShouldSkipPoll()) {
        return false;
    }

    int statusCode;
    bool success = Get(endpoint, response, statusCode);

    UpdatePollingInterval(success && statusCode == 200);

    if(m_logger != NULL && success && statusCode == 200) {
        m_logger.Debug("Poll successful, response length: " + IntegerToString(StringLen(response)));
    }

    return success && statusCode == 200;
}

//+------------------------------------------------------------------+
//| Test connection health                                           |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::TestConnection() {
    string response;
    int statusCode;

    string healthUrl = m_baseUrl + "/health";
    string headers = "User-Agent: " + m_userAgent + "\r\n";

    if(SendSingleRequest("GET", healthUrl, headers, "", response, statusCode)) {
        bool healthy = statusCode == 200;
        if(m_logger != NULL) {
            m_logger.Info(StringFormat("Connection test %s (status: %d)",
                         healthy ? "passed" : "failed", statusCode));
        }
        return healthy;
    }

    if(m_logger != NULL) {
        m_logger.Warning("Connection test failed - network error");
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if client is healthy                                      |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::IsHealthy() {
    // Consider healthy if:
    // 1. Circuit breaker is closed
    // 2. Recent successful requests
    // 3. Error rate is acceptable

    if(m_circuitBreakerOpen) {
        return false;
    }

    // Check if we had recent successful requests (within last 5 minutes)
    if(TimeCurrent() - m_lastSuccessfulRequest > 300) {
        return false;
    }

    // Check error rate (should be less than 50%)
    if(m_stats.totalRequests > 10) {
        double errorRate = (double)m_stats.failedRequests / m_stats.totalRequests;
        if(errorRate > 0.5) {
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Set polling configuration                                        |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::SetPollingConfig(int baseInterval, int maxInterval,
                                          int backoffMultiplier, int maxErrors, bool adaptive = true) {
    m_pollingConfig.baseInterval = MathMax(1, baseInterval);
    m_pollingConfig.maxInterval = MathMax(baseInterval, maxInterval);
    m_pollingConfig.backoffMultiplier = MathMax(1, backoffMultiplier);
    m_pollingConfig.maxConsecutiveErrors = MathMax(1, maxErrors);
    m_pollingConfig.adaptivePolling = adaptive;

    m_currentInterval = m_pollingConfig.baseInterval;

    if(m_logger != NULL) {
        m_logger.Info(StringFormat("Polling config updated: base=%d, max=%d, backoff=%d, maxErrors=%d, adaptive=%s",
                     baseInterval, maxInterval, backoffMultiplier, maxErrors, adaptive ? "true" : "false"));
    }
}

//+------------------------------------------------------------------+
//| Reset statistics                                                 |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::ResetStats() {
    m_stats.totalRequests = 0;
    m_stats.successfulRequests = 0;
    m_stats.failedRequests = 0;
    m_stats.avgResponseTime = 0.0;
    m_stats.lastStatsReset = TimeCurrent();

    if(m_logger != NULL) {
        m_logger.Info("HTTP client statistics reset");
    }
}

//+------------------------------------------------------------------+
//| Get health report                                                |
//+------------------------------------------------------------------+
string CEnhancedHttpClient::GetHealthReport() {
    double errorRate = 0.0;
    if(m_stats.totalRequests > 0) {
        errorRate = (double)m_stats.failedRequests / m_stats.totalRequests * 100.0;
    }

    string report = StringFormat(
        "HTTP Client Health Report:\n"
        "- Status: %s\n"
        "- Circuit Breaker: %s\n"
        "- Total Requests: %d\n"
        "- Success Rate: %.1f%%\n"
        "- Avg Response Time: %.1fms\n"
        "- Current Polling Interval: %ds\n"
        "- Consecutive Errors: %d\n"
        "- Queue Size: %d\n"
        "- Last Success: %s",
        IsHealthy() ? "Healthy" : "Unhealthy",
        m_circuitBreakerOpen ? "Open" : "Closed",
        m_stats.totalRequests,
        100.0 - errorRate,
        m_stats.avgResponseTime,
        m_currentInterval,
        m_consecutiveErrors,
        GetQueueSize(),
        TimeToString(m_lastSuccessfulRequest)
    );

    return report;
}

//+------------------------------------------------------------------+
//| Queue request for offline processing                            |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::QueueRequest(string method, string endpoint, string data = "") {
    int currentSize = ArraySize(m_queuedRequests);

    // Check queue size limit
    if(currentSize >= m_maxQueueSize) {
        if(m_logger != NULL) {
            m_logger.Warning("Request queue is full, dropping oldest request");
        }
        // Remove oldest request (FIFO)
        for(int i = 0; i < currentSize - 1; i++) {
            m_queuedRequests[i] = m_queuedRequests[i + 1];
        }
        currentSize--;
    }

    // Add new request
    ArrayResize(m_queuedRequests, currentSize + 1);
    string queuedRequest = StringFormat("%s|%s|%s|%d", method, endpoint, data, TimeCurrent());
    m_queuedRequests[currentSize] = queuedRequest;

    if(m_logger != NULL) {
        m_logger.Debug(StringFormat("Queued %s request to %s (queue size: %d)",
                      method, endpoint, currentSize + 1));
    }
}

//+------------------------------------------------------------------+
//| Process queued requests                                          |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::ProcessQueuedRequests() {
    if(IsCircuitBreakerOpen() || ArraySize(m_queuedRequests) == 0) {
        return false;
    }

    bool anyProcessed = false;
    int queueSize = ArraySize(m_queuedRequests);

    for(int i = queueSize - 1; i >= 0; i--) {
        string queuedRequest = m_queuedRequests[i];
        string parts[];
        int partCount = StringSplit(queuedRequest, '|', parts);

        if(partCount >= 4) {
            string method = parts[0];
            string endpoint = parts[1];
            string data = parts[2];
            datetime timestamp = (datetime)StringToInteger(parts[3]);

            // Skip if too old (1 hour)
            if(TimeCurrent() - timestamp > 3600) {
                // Remove old request
                for(int j = i; j < queueSize - 1; j++) {
                    m_queuedRequests[j] = m_queuedRequests[j + 1];
                }
                ArrayResize(m_queuedRequests, queueSize - 1);
                queueSize--;
                continue;
            }

            // Try to process request
            string response;
            int statusCode;
            bool success = false;

            if(method == "GET") {
                success = Get(endpoint, response, statusCode);
            } else if(method == "POST") {
                success = Post(endpoint, data, response, statusCode);
            } else if(method == "PUT") {
                success = Put(endpoint, data, response, statusCode);
            } else if(method == "DELETE") {
                success = Delete(endpoint, response, statusCode);
            }

            if(success && statusCode >= 200 && statusCode < 300) {
                // Success - remove from queue
                for(int j = i; j < queueSize - 1; j++) {
                    m_queuedRequests[j] = m_queuedRequests[j + 1];
                }
                ArrayResize(m_queuedRequests, queueSize - 1);
                queueSize--;
                anyProcessed = true;

                if(m_logger != NULL) {
                    m_logger.Debug(StringFormat("Successfully processed queued %s request to %s",
                                  method, endpoint));
                }
            }
        }
    }

    return anyProcessed;
}
