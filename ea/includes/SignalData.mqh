//+------------------------------------------------------------------+
//| SignalData.mqh                                                   |
//| Signal data structures for Copy Trading System                  |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

//+------------------------------------------------------------------+
//| Signal data structure                                            |
//+------------------------------------------------------------------+
struct SignalData {
    string signal_id;
    string provider_id;
    string action;          // "open", "close", "modify"
    string symbol;
    string type;            // "buy", "sell"
    double volume;
    double open_price;
    double stop_loss;
    double take_profit;
    string comment;
    int original_ticket;
    // Partial close fields
    double partial_close_volume;
    double remaining_volume;
    bool is_partial_close;
    string parent_signal_id;
    datetime timestamp;
};

//+------------------------------------------------------------------+
//| Execution result structure                                       |
//+------------------------------------------------------------------+
struct ExecutionResult {
    string execution_id;
    string signal_id;
    string follower_id;
    bool success;
    ulong ticket;
    double executed_volume;
    double executed_price;
    string error_message;
    int error_code;
    datetime execution_time;
};

//+------------------------------------------------------------------+
//| Position tracking structure                                      |
//+------------------------------------------------------------------+
struct PositionTracker {
    ulong ticket;
    string signal_id;
    string symbol;
    double original_volume;
    double current_volume;
    double closed_volume;
    bool is_partial_close;
    datetime opened_at;
    datetime last_update;
};

//+------------------------------------------------------------------+
//| Helper functions for SignalData                                 |
//+------------------------------------------------------------------+
class CSignalDataHelper {
public:
    // Initialize signal data
    static void InitializeSignalData(SignalData &signal) {
        signal.signal_id = "";
        signal.provider_id = "";
        signal.action = "";
        signal.symbol = "";
        signal.type = "";
        signal.volume = 0;
        signal.open_price = 0;
        signal.stop_loss = 0;
        signal.take_profit = 0;
        signal.comment = "";
        signal.original_ticket = 0;
        signal.partial_close_volume = 0;
        signal.remaining_volume = 0;
        signal.is_partial_close = false;
        signal.parent_signal_id = "";
        signal.timestamp = 0;
    }
    
    // Create signal from position
    static bool CreateSignalFromPosition(ulong ticket, SignalData &signal, string providerId) {
        if(!PositionSelectByTicket(ticket)) {
            return false;
        }
        
        InitializeSignalData(signal);
        
        signal.provider_id = providerId;
        signal.action = "open";
        signal.symbol = PositionGetString(POSITION_SYMBOL);
        signal.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "buy" : "sell";
        signal.volume = PositionGetDouble(POSITION_VOLUME);
        signal.open_price = PositionGetDouble(POSITION_PRICE_OPEN);
        signal.stop_loss = PositionGetDouble(POSITION_SL);
        signal.take_profit = PositionGetDouble(POSITION_TP);
        signal.comment = PositionGetString(POSITION_COMMENT);
        signal.original_ticket = (int)ticket;
        signal.timestamp = TimeCurrent();
        
        // Generate unique signal ID
        signal.signal_id = StringFormat("%s_%d_%d", providerId, ticket, TimeCurrent());
        
        return true;
    }
    
    // Create partial close signal
    static bool CreatePartialCloseSignal(ulong ticket, double closedVolume, 
                                        SignalData &signal, string providerId) {
        if(!PositionSelectByTicket(ticket)) {
            return false;
        }
        
        InitializeSignalData(signal);
        
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double originalVolume = currentVolume + closedVolume;
        
        signal.provider_id = providerId;
        signal.action = "close";
        signal.symbol = PositionGetString(POSITION_SYMBOL);
        signal.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "buy" : "sell";
        signal.volume = closedVolume;
        signal.original_ticket = (int)ticket;
        signal.partial_close_volume = closedVolume;
        signal.remaining_volume = currentVolume;
        signal.is_partial_close = true;
        signal.comment = StringFormat("Partial close %.2f/%.2f", closedVolume, originalVolume);
        signal.timestamp = TimeCurrent();
        
        // Generate unique signal ID for partial close
        signal.signal_id = StringFormat("partial_%s_%d_%d", providerId, ticket, TimeCurrent());
        
        return true;
    }
    
    // Create close signal
    static bool CreateCloseSignal(ulong ticket, SignalData &signal, string providerId) {
        // For closed positions, we need to check history
        if(!HistorySelectByPosition(ticket)) {
            return false;
        }
        
        InitializeSignalData(signal);
        
        // Get position details from history
        int totalDeals = HistoryDealsTotal();
        for(int i = 0; i < totalDeals; i++) {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket) {
                ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(dealTicket, DEAL_ENTRY);
                
                if(entry == DEAL_ENTRY_IN) {
                    signal.symbol = HistoryDealGetString(dealTicket, DEAL_SYMBOL);
                    signal.type = (HistoryDealGetInteger(dealTicket, DEAL_TYPE) == DEAL_TYPE_BUY) ? "buy" : "sell";
                    signal.volume = HistoryDealGetDouble(dealTicket, DEAL_VOLUME);
                    break;
                }
            }
        }
        
        signal.provider_id = providerId;
        signal.action = "close";
        signal.original_ticket = (int)ticket;
        signal.comment = "Position closed";
        signal.timestamp = TimeCurrent();
        
        // Generate unique signal ID
        signal.signal_id = StringFormat("close_%s_%d_%d", providerId, ticket, TimeCurrent());
        
        return true;
    }
    
    // Create modify signal
    static bool CreateModifySignal(ulong ticket, double newSL, double newTP, 
                                  SignalData &signal, string providerId) {
        if(!PositionSelectByTicket(ticket)) {
            return false;
        }
        
        InitializeSignalData(signal);
        
        signal.provider_id = providerId;
        signal.action = "modify";
        signal.symbol = PositionGetString(POSITION_SYMBOL);
        signal.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "buy" : "sell";
        signal.volume = PositionGetDouble(POSITION_VOLUME);
        signal.stop_loss = newSL;
        signal.take_profit = newTP;
        signal.original_ticket = (int)ticket;
        signal.comment = "Position modified";
        signal.timestamp = TimeCurrent();
        
        // Generate unique signal ID
        signal.signal_id = StringFormat("modify_%s_%d_%d", providerId, ticket, TimeCurrent());
        
        return true;
    }
    
    // Validate signal data
    static bool ValidateSignal(const SignalData &signal) {
        // Check required fields
        if(StringLen(signal.provider_id) == 0) return false;
        if(StringLen(signal.action) == 0) return false;
        if(StringLen(signal.symbol) == 0) return false;
        
        // Validate action
        if(signal.action != "open" && signal.action != "close" && signal.action != "modify") {
            return false;
        }
        
        // Validate volume for open signals
        if(signal.action == "open" && signal.volume <= 0) {
            return false;
        }
        
        // Validate partial close
        if(signal.is_partial_close) {
            if(signal.action != "close") return false;
            if(signal.partial_close_volume <= 0) return false;
            if(signal.remaining_volume <= 0) return false;
        }
        
        return true;
    }
    
    // Convert signal to JSON string
    static string SignalToJson(const SignalData &signal) {
        string json = StringFormat(
            "{"
            "\"signal_id\":\"%s\","
            "\"provider_id\":\"%s\","
            "\"action\":\"%s\","
            "\"symbol\":\"%s\","
            "\"type\":\"%s\","
            "\"volume\":%.2f,"
            "\"open_price\":%.5f,"
            "\"stop_loss\":%.5f,"
            "\"take_profit\":%.5f,"
            "\"comment\":\"%s\","
            "\"original_ticket\":%d,"
            "\"partial_close_volume\":%.2f,"
            "\"remaining_volume\":%.2f,"
            "\"is_partial_close\":%s,"
            "\"parent_signal_id\":\"%s\","
            "\"timestamp\":%d"
            "}",
            signal.signal_id,
            signal.provider_id,
            signal.action,
            signal.symbol,
            signal.type,
            signal.volume,
            signal.open_price,
            signal.stop_loss,
            signal.take_profit,
            signal.comment,
            signal.original_ticket,
            signal.partial_close_volume,
            signal.remaining_volume,
            signal.is_partial_close ? "true" : "false",
            signal.parent_signal_id,
            signal.timestamp
        );
        
        return json;
    }
};
