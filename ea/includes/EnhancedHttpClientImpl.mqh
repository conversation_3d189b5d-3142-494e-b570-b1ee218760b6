//+------------------------------------------------------------------+
//| EnhancedHttpClientImpl.mqh                                       |
//| Implementation methods for Enhanced HTTP Client                 |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"

//+------------------------------------------------------------------+
//| Send request with retry logic                                   |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::SendRequestWithRetry(string method, string url, string headers, 
                                               string data, string &response, int &statusCode, int maxRetries = 3) {
    for(int attempt = 1; attempt <= maxRetries; attempt++) {
        if(SendSingleRequest(method, url, headers, data, response, statusCode)) {
            if(statusCode >= 200 && statusCode < 300) {
                return true;
            }
            
            // Check if we should retry based on status code
            if(statusCode >= 500 && statusCode < 600) {
                if(m_logger != NULL) {
                    m_logger.Warning(StringFormat("Server error %d on attempt %d, will retry", statusCode, attempt));
                }
            } else {
                // Client error, don't retry
                return false;
            }
        }
        
        if(attempt < maxRetries) {
            int delay = 1000 * attempt; // Exponential backoff
            if(m_logger != NULL) {
                m_logger.Debug(StringFormat("Request attempt %d failed, retrying in %dms", attempt, delay));
            }
            Sleep(delay);
        }
    }
    
    if(m_logger != NULL) {
        m_logger.Error(StringFormat("Request failed after %d attempts", maxRetries));
    }
    return false;
}

//+------------------------------------------------------------------+
//| Send single HTTP request                                         |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::SendSingleRequest(string method, string url, string headers, 
                                            string data, string &response, int &statusCode) {
    char postData[];
    char resultData[];
    string resultHeaders;
    
    // Convert data to char array
    if(StringLen(data) > 0) {
        StringToCharArray(data, postData, 0, StringLen(data));
    }
    
    // Send request
    int result = WebRequest(method, url, headers, m_timeout, postData, resultData, resultHeaders);
    
    // Parse response
    if(result > 0) {
        statusCode = result;
        response = CharArrayToString(resultData);
        return true;
    } else {
        statusCode = result;
        response = "";
        
        // Log error details
        int error = GetLastError();
        if(m_logger != NULL) {
            m_logger.Error(StringFormat("WebRequest failed. Error: %d (%s), URL: %s", 
                          error, GetErrorDescription(error), url));
        }
        
        return false;
    }
}

//+------------------------------------------------------------------+
//| Update polling interval based on success/failure                |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::UpdatePollingInterval(bool success) {
    if(!m_pollingConfig.adaptivePolling) {
        return;
    }
    
    if(success) {
        // Gradually decrease interval on success (more frequent polling)
        if(m_currentInterval > m_pollingConfig.baseInterval) {
            m_currentInterval = MathMax(m_pollingConfig.baseInterval, m_currentInterval - 1);
        }
    } else {
        // Increase interval on failure (less frequent polling)
        m_currentInterval = MathMin(m_pollingConfig.maxInterval, 
                                   m_currentInterval * m_pollingConfig.backoffMultiplier);
    }
    
    if(m_logger != NULL) {
        m_logger.Debug(StringFormat("Polling interval updated to %d seconds (success: %s)", 
                      m_currentInterval, success ? "true" : "false"));
    }
}

//+------------------------------------------------------------------+
//| Check if we should skip current poll                            |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::ShouldSkipPoll() {
    // Skip if circuit breaker is open
    if(m_circuitBreakerOpen) {
        return true;
    }
    
    // Skip if too many consecutive errors
    if(m_consecutiveErrors >= m_pollingConfig.maxConsecutiveErrors) {
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Update circuit breaker state                                    |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::UpdateCircuitBreaker(bool success) {
    if(success) {
        m_consecutiveErrors = 0;
        m_lastSuccessfulRequest = TimeCurrent();
        
        // Close circuit breaker if it was open
        if(m_circuitBreakerOpen) {
            m_circuitBreakerOpen = false;
            if(m_logger != NULL) {
                m_logger.Info("Circuit breaker closed - connection restored");
            }
        }
    } else {
        m_consecutiveErrors++;
        
        // Open circuit breaker if too many consecutive errors
        if(m_consecutiveErrors >= m_pollingConfig.maxConsecutiveErrors && !m_circuitBreakerOpen) {
            m_circuitBreakerOpen = true;
            if(m_logger != NULL) {
                m_logger.Warning(StringFormat("Circuit breaker opened after %d consecutive errors", 
                                m_consecutiveErrors));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if circuit breaker is open                                |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::IsCircuitBreakerOpen() {
    // Auto-reset circuit breaker after timeout
    if(m_circuitBreakerOpen && TimeCurrent() - m_lastSuccessfulRequest > 300) { // 5 minutes
        if(TestConnection()) {
            m_circuitBreakerOpen = false;
            m_consecutiveErrors = 0;
            if(m_logger != NULL) {
                m_logger.Info("Circuit breaker auto-reset successful");
            }
        }
    }
    
    return m_circuitBreakerOpen;
}

//+------------------------------------------------------------------+
//| Update request statistics                                        |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::UpdateStats(bool success, double responseTime) {
    m_stats.totalRequests++;
    
    if(success) {
        m_stats.successfulRequests++;
    } else {
        m_stats.failedRequests++;
    }
    
    // Update average response time using exponential moving average
    if(m_stats.avgResponseTime == 0.0) {
        m_stats.avgResponseTime = responseTime;
    } else {
        m_stats.avgResponseTime = (m_stats.avgResponseTime * 0.9) + (responseTime * 0.1);
    }
}

//+------------------------------------------------------------------+
//| Build complete URL                                               |
//+------------------------------------------------------------------+
string CEnhancedHttpClient::BuildUrl(string endpoint) {
    if(StringSubstr(endpoint, 0, 1) != "/") {
        endpoint = "/" + endpoint;
    }
    
    return m_baseUrl + endpoint;
}

//+------------------------------------------------------------------+
//| Build HTTP headers                                               |
//+------------------------------------------------------------------+
string CEnhancedHttpClient::BuildHeaders(string contentType = "application/json") {
    string headers = "";
    
    // Authorization header
    if(StringLen(m_apiKey) > 0) {
        headers += "Authorization: Bearer " + m_apiKey + "\r\n";
    }
    
    // Content-Type header
    if(StringLen(contentType) > 0) {
        headers += "Content-Type: " + contentType + "\r\n";
    }
    
    // User-Agent header
    headers += "User-Agent: " + m_userAgent + "\r\n";
    
    // Accept header
    headers += "Accept: application/json\r\n";
    
    // Connection header for keep-alive
    headers += "Connection: keep-alive\r\n";
    
    // Cache control
    headers += "Cache-Control: no-cache\r\n";
    
    return headers;
}

//+------------------------------------------------------------------+
//| Check if error is retryable                                     |
//+------------------------------------------------------------------+
bool CEnhancedHttpClient::IsRetryableError(int errorCode) {
    switch(errorCode) {
        case 4060: // No connection
        case 4062: // Timeout
        case -1:   // General network error
            return true;
        default:
            return false;
    }
}

//+------------------------------------------------------------------+
//| Get error description                                            |
//+------------------------------------------------------------------+
string CEnhancedHttpClient::GetErrorDescription(int errorCode) {
    switch(errorCode) {
        case 4060: return "No connection to server";
        case 4061: return "URL not found";
        case 4062: return "Request timeout";
        case 4014: return "Function not allowed in testing mode";
        case 5203: return "Object does not exist";
        default: return StringFormat("Unknown error: %d", errorCode);
    }
}

//+------------------------------------------------------------------+
//| URL Encode string                                                |
//+------------------------------------------------------------------+
string CEnhancedHttpClient::UrlEncode(string str) {
    string result = "";
    
    for(int i = 0; i < StringLen(str); i++) {
        ushort ch = StringGetCharacter(str, i);
        
        if((ch >= 'A' && ch <= 'Z') || 
           (ch >= 'a' && ch <= 'z') || 
           (ch >= '0' && ch <= '9') ||
           ch == '-' || ch == '_' || ch == '.' || ch == '~') {
            result += CharToString((char)ch);
        } else {
            result += StringFormat("%%%02X", ch);
        }
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Reset connection pool                                            |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::ResetConnectionPool() {
    for(int i = 0; i < ArraySize(m_connectionPool); i++) {
        m_connectionPool[i].url = "";
        m_connectionPool[i].lastUsed = 0;
        m_connectionPool[i].inUse = false;
        m_connectionPool[i].connectionId = 0;
        m_connectionPool[i].failureCount = 0;
    }
    
    if(m_logger != NULL) {
        m_logger.Info("Connection pool reset");
    }
}

//+------------------------------------------------------------------+
//| Stop polling                                                     |
//+------------------------------------------------------------------+
void CEnhancedHttpClient::StopPolling() {
    // Reset polling state
    m_currentInterval = m_pollingConfig.baseInterval;
    m_consecutiveErrors = 0;
    
    if(m_logger != NULL) {
        m_logger.Info("Polling stopped");
    }
}
