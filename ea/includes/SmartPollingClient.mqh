//+------------------------------------------------------------------+
//| SmartPollingClient.mqh                                           |
//| Smart HTTP polling client with adaptive intervals               |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"

#include "EnhancedHttpClient.mqh"
#include "Logger.mqh"

//--- Polling statistics
struct PollingStats {
    int totalPolls;
    int successfulPolls;
    int emptyResponses;
    int errorCount;
    datetime lastActivity;
    double avgResponseSize;
};

//--- Signal callback function type
typedef void (*SignalCallback)(string signalJson);

//+------------------------------------------------------------------+
//| Smart Polling Client Class                                       |
//+------------------------------------------------------------------+
class CSmartPollingClient {
private:
    CEnhancedHttpClient* m_httpClient;
    CLogger*            m_logger;
    
    // Polling configuration
    string              m_baseUrl;
    string              m_apiKey;
    string              m_followerId;
    string              m_pollEndpoint;
    
    // Polling state
    bool                m_isPolling;
    datetime            m_lastPollTime;
    datetime            m_lastSignalTime;
    int                 m_currentInterval;
    
    // Adaptive polling parameters
    int                 m_baseInterval;      // Base polling interval (seconds)
    int                 m_maxInterval;       // Maximum polling interval (seconds)
    int                 m_minInterval;       // Minimum polling interval (seconds)
    int                 m_idleMultiplier;    // Multiplier when no activity
    int                 m_activeMultiplier;  // Multiplier when active
    
    // Statistics and monitoring
    PollingStats        m_stats;
    SignalCallback      m_signalCallback;
    
    // Message deduplication
    string              m_lastSignalIds[];
    int                 m_maxSignalHistory;
    
    // Connection health
    bool                m_connectionHealthy;
    datetime            m_lastHealthCheck;
    int                 m_healthCheckInterval;

public:
    //--- Constructor
    CSmartPollingClient(string baseUrl, string apiKey, string followerId, CLogger* logger = NULL);
    
    //--- Destructor
    ~CSmartPollingClient();
    
    //--- Main polling methods
    bool StartPolling();
    void StopPolling();
    bool IsPolling() { return m_isPolling; }
    
    //--- Polling execution
    void OnTick();
    bool PollForSignals();
    
    //--- Configuration
    void SetPollingIntervals(int baseInterval, int minInterval, int maxInterval);
    void SetAdaptiveMultipliers(int idleMultiplier, int activeMultiplier);
    void SetSignalCallback(SignalCallback callback) { m_signalCallback = callback; }
    void SetPollEndpoint(string endpoint) { m_pollEndpoint = endpoint; }
    
    //--- Statistics and monitoring
    PollingStats GetStats() { return m_stats; }
    void ResetStats();
    string GetStatusReport();
    bool IsConnectionHealthy() { return m_connectionHealthy; }
    
    //--- Health management
    bool CheckConnectionHealth();
    void UpdateConnectionHealth(bool healthy);

private:
    //--- Internal polling logic
    bool ShouldPoll();
    void UpdatePollingInterval(bool hasActivity);
    bool ProcessPollingResponse(string response);
    
    //--- Signal processing
    bool ParseSignalsFromResponse(string response, string &signals[]);
    bool IsSignalDuplicate(string signalId);
    void AddSignalToHistory(string signalId);
    void CleanupSignalHistory();
    
    //--- Statistics tracking
    void UpdatePollingStats(bool success, int responseSize);
    
    //--- Helper methods
    string BuildPollUrl();
    bool ValidateSignalData(string signalJson);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CSmartPollingClient::CSmartPollingClient(string baseUrl, string apiKey, string followerId, CLogger* logger = NULL) {
    m_baseUrl = baseUrl;
    m_apiKey = apiKey;
    m_followerId = followerId;
    m_logger = logger;
    
    // Initialize HTTP client
    m_httpClient = new CEnhancedHttpClient(baseUrl, apiKey, logger);
    
    // Set default polling configuration
    m_baseInterval = 1;          // 1 second base interval
    m_minInterval = 1;           // Minimum 1 second
    m_maxInterval = 30;          // Maximum 30 seconds
    m_idleMultiplier = 2;        // Double interval when idle
    m_activeMultiplier = 1;      // Keep interval when active
    
    m_currentInterval = m_baseInterval;
    m_pollEndpoint = "/api/v1/signals/poll";
    
    // Initialize state
    m_isPolling = false;
    m_lastPollTime = 0;
    m_lastSignalTime = 0;
    m_signalCallback = NULL;
    
    // Initialize signal history
    m_maxSignalHistory = 100;
    ArrayResize(m_lastSignalIds, 0);
    
    // Initialize health monitoring
    m_connectionHealthy = true;
    m_lastHealthCheck = 0;
    m_healthCheckInterval = 60; // Check every minute
    
    // Reset statistics
    ResetStats();
    
    if(m_logger != NULL) {
        m_logger.Info(StringFormat("Smart Polling Client initialized for follower: %s", m_followerId));
    }
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CSmartPollingClient::~CSmartPollingClient() {
    StopPolling();
    
    if(m_httpClient != NULL) {
        delete m_httpClient;
        m_httpClient = NULL;
    }
    
    if(m_logger != NULL) {
        m_logger.Info("Smart Polling Client destroyed");
    }
}

//+------------------------------------------------------------------+
//| Start polling for signals                                        |
//+------------------------------------------------------------------+
bool CSmartPollingClient::StartPolling() {
    if(m_isPolling) {
        if(m_logger != NULL) {
            m_logger.Warning("Polling is already active");
        }
        return false;
    }
    
    // Test connection before starting
    if(!CheckConnectionHealth()) {
        if(m_logger != NULL) {
            m_logger.Error("Cannot start polling - connection health check failed");
        }
        return false;
    }
    
    m_isPolling = true;
    m_lastPollTime = TimeCurrent();
    m_currentInterval = m_baseInterval;
    
    if(m_logger != NULL) {
        m_logger.Info("Smart polling started");
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Stop polling                                                     |
//+------------------------------------------------------------------+
void CSmartPollingClient::StopPolling() {
    if(!m_isPolling) {
        return;
    }
    
    m_isPolling = false;
    
    if(m_logger != NULL) {
        m_logger.Info("Smart polling stopped");
    }
}

//+------------------------------------------------------------------+
//| Main tick handler - call this from EA OnTick()                  |
//+------------------------------------------------------------------+
void CSmartPollingClient::OnTick() {
    if(!m_isPolling) {
        return;
    }
    
    // Check if it's time to poll
    if(ShouldPoll()) {
        PollForSignals();
    }
    
    // Periodic health check
    if(TimeCurrent() - m_lastHealthCheck > m_healthCheckInterval) {
        CheckConnectionHealth();
        m_lastHealthCheck = TimeCurrent();
    }
    
    // Process any queued requests
    if(m_httpClient != NULL) {
        m_httpClient.ProcessQueuedRequests();
    }
    
    // Cleanup old signal history
    CleanupSignalHistory();
}

//+------------------------------------------------------------------+
//| Poll for new signals                                             |
//+------------------------------------------------------------------+
bool CSmartPollingClient::PollForSignals() {
    if(!m_isPolling || m_httpClient == NULL) {
        return false;
    }
    
    string response;
    string pollUrl = BuildPollUrl();
    
    // Use the enhanced HTTP client's polling method
    bool success = m_httpClient.PollOnce(m_pollEndpoint + "?follower_id=" + m_followerId, response);
    
    m_lastPollTime = TimeCurrent();
    
    // Update statistics
    UpdatePollingStats(success, StringLen(response));
    
    if(success) {
        // Process the response
        bool hasActivity = ProcessPollingResponse(response);
        UpdatePollingInterval(hasActivity);
        UpdateConnectionHealth(true);
        
        if(hasActivity && m_logger != NULL) {
            m_logger.Debug("Polling detected activity, response processed");
        }
        
        return hasActivity;
    } else {
        UpdateConnectionHealth(false);
        UpdatePollingInterval(false);
        
        if(m_logger != NULL) {
            m_logger.Warning("Polling request failed");
        }
        
        return false;
    }
}

//+------------------------------------------------------------------+
//| Check if we should poll now                                     |
//+------------------------------------------------------------------+
bool CSmartPollingClient::ShouldPoll() {
    // Don't poll if connection is unhealthy
    if(!m_connectionHealthy) {
        return false;
    }
    
    // Check if enough time has passed since last poll
    return (TimeCurrent() - m_lastPollTime) >= m_currentInterval;
}

//+------------------------------------------------------------------+
//| Update polling interval based on activity                       |
//+------------------------------------------------------------------+
void CSmartPollingClient::UpdatePollingInterval(bool hasActivity) {
    int newInterval;
    
    if(hasActivity) {
        // Decrease interval when there's activity (more frequent polling)
        newInterval = MathMax(m_minInterval, m_currentInterval / m_activeMultiplier);
        m_lastSignalTime = TimeCurrent();
    } else {
        // Increase interval when idle (less frequent polling)
        // But only if we've been idle for a while
        if(TimeCurrent() - m_lastSignalTime > 60) { // 1 minute of inactivity
            newInterval = MathMin(m_maxInterval, m_currentInterval * m_idleMultiplier);
        } else {
            newInterval = m_currentInterval;
        }
    }
    
    if(newInterval != m_currentInterval) {
        m_currentInterval = newInterval;
        if(m_logger != NULL) {
            m_logger.Debug(StringFormat("Polling interval updated to %d seconds (activity: %s)",
                          m_currentInterval, hasActivity ? "yes" : "no"));
        }
    }
}

//+------------------------------------------------------------------+
//| Process polling response and extract signals                    |
//+------------------------------------------------------------------+
bool CSmartPollingClient::ProcessPollingResponse(string response) {
    if(StringLen(response) == 0) {
        return false;
    }

    // Parse signals from response
    string signals[];
    if(!ParseSignalsFromResponse(response, signals)) {
        return false;
    }

    bool hasNewSignals = false;

    // Process each signal
    for(int i = 0; i < ArraySize(signals); i++) {
        string signalJson = signals[i];

        if(!ValidateSignalData(signalJson)) {
            if(m_logger != NULL) {
                m_logger.Warning("Invalid signal data received, skipping");
            }
            continue;
        }

        // Extract signal ID for deduplication
        string signalId = "";
        // Simple JSON parsing to get signal_id
        int idStart = StringFind(signalJson, "\"signal_id\":\"");
        if(idStart >= 0) {
            idStart += 13; // Length of "signal_id":""
            int idEnd = StringFind(signalJson, "\"", idStart);
            if(idEnd > idStart) {
                signalId = StringSubstr(signalJson, idStart, idEnd - idStart);
            }
        }

        // Check for duplicates
        if(StringLen(signalId) > 0 && IsSignalDuplicate(signalId)) {
            if(m_logger != NULL) {
                m_logger.Debug("Duplicate signal detected, skipping: " + signalId);
            }
            continue;
        }

        // Add to history
        if(StringLen(signalId) > 0) {
            AddSignalToHistory(signalId);
        }

        // Call signal callback
        if(m_signalCallback != NULL) {
            m_signalCallback(signalJson);
            hasNewSignals = true;
        }

        if(m_logger != NULL) {
            m_logger.Info("New signal processed: " + signalId);
        }
    }

    return hasNewSignals;
}

//+------------------------------------------------------------------+
//| Parse signals from JSON response                                |
//+------------------------------------------------------------------+
bool CSmartPollingClient::ParseSignalsFromResponse(string response, string &signals[]) {
    ArrayResize(signals, 0);

    // Simple JSON array parsing
    // Look for "signals": [...]
    int signalsStart = StringFind(response, "\"signals\":");
    if(signalsStart < 0) {
        return false;
    }

    int arrayStart = StringFind(response, "[", signalsStart);
    if(arrayStart < 0) {
        return false;
    }

    int arrayEnd = StringFind(response, "]", arrayStart);
    if(arrayEnd < 0) {
        return false;
    }

    string signalsArray = StringSubstr(response, arrayStart + 1, arrayEnd - arrayStart - 1);

    // Split by objects (simple approach)
    int signalCount = 0;
    int pos = 0;

    while(pos < StringLen(signalsArray)) {
        int objStart = StringFind(signalsArray, "{", pos);
        if(objStart < 0) break;

        int objEnd = StringFind(signalsArray, "}", objStart);
        if(objEnd < 0) break;

        string signalJson = StringSubstr(signalsArray, objStart, objEnd - objStart + 1);

        ArrayResize(signals, signalCount + 1);
        signals[signalCount] = signalJson;
        signalCount++;

        pos = objEnd + 1;
    }

    return signalCount > 0;
}

//+------------------------------------------------------------------+
//| Check if signal is duplicate                                    |
//+------------------------------------------------------------------+
bool CSmartPollingClient::IsSignalDuplicate(string signalId) {
    for(int i = 0; i < ArraySize(m_lastSignalIds); i++) {
        if(m_lastSignalIds[i] == signalId) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add signal ID to history                                        |
//+------------------------------------------------------------------+
void CSmartPollingClient::AddSignalToHistory(string signalId) {
    int currentSize = ArraySize(m_lastSignalIds);

    // Check if we need to remove old entries
    if(currentSize >= m_maxSignalHistory) {
        // Remove oldest entry (FIFO)
        for(int i = 0; i < currentSize - 1; i++) {
            m_lastSignalIds[i] = m_lastSignalIds[i + 1];
        }
        currentSize--;
    }

    // Add new signal ID
    ArrayResize(m_lastSignalIds, currentSize + 1);
    m_lastSignalIds[currentSize] = signalId;
}

//+------------------------------------------------------------------+
//| Cleanup old signal history                                      |
//+------------------------------------------------------------------+
void CSmartPollingClient::CleanupSignalHistory() {
    // Remove signal IDs older than 1 hour
    // This is a simple implementation - in practice you might want to store timestamps
    if(ArraySize(m_lastSignalIds) > m_maxSignalHistory / 2) {
        int newSize = m_maxSignalHistory / 2;
        int oldSize = ArraySize(m_lastSignalIds);

        // Keep only the most recent half
        for(int i = 0; i < newSize; i++) {
            m_lastSignalIds[i] = m_lastSignalIds[oldSize - newSize + i];
        }

        ArrayResize(m_lastSignalIds, newSize);

        if(m_logger != NULL) {
            m_logger.Debug("Signal history cleaned up");
        }
    }
}

//+------------------------------------------------------------------+
//| Update polling statistics                                        |
//+------------------------------------------------------------------+
void CSmartPollingClient::UpdatePollingStats(bool success, int responseSize) {
    m_stats.totalPolls++;

    if(success) {
        m_stats.successfulPolls++;

        if(responseSize == 0) {
            m_stats.emptyResponses++;
        } else {
            m_stats.lastActivity = TimeCurrent();

            // Update average response size
            if(m_stats.avgResponseSize == 0.0) {
                m_stats.avgResponseSize = responseSize;
            } else {
                m_stats.avgResponseSize = (m_stats.avgResponseSize * 0.9) + (responseSize * 0.1);
            }
        }
    } else {
        m_stats.errorCount++;
    }
}

//+------------------------------------------------------------------+
//| Build poll URL with parameters                                  |
//+------------------------------------------------------------------+
string CSmartPollingClient::BuildPollUrl() {
    string url = m_pollEndpoint;
    url += "?follower_id=" + m_followerId;
    url += "&timestamp=" + IntegerToString(TimeCurrent());

    return url;
}

//+------------------------------------------------------------------+
//| Validate signal data                                            |
//+------------------------------------------------------------------+
bool CSmartPollingClient::ValidateSignalData(string signalJson) {
    // Basic validation - check for required fields
    if(StringFind(signalJson, "signal_id") < 0) return false;
    if(StringFind(signalJson, "provider_id") < 0) return false;
    if(StringFind(signalJson, "action") < 0) return false;

    return true;
}

//+------------------------------------------------------------------+
//| Check connection health                                          |
//+------------------------------------------------------------------+
bool CSmartPollingClient::CheckConnectionHealth() {
    if(m_httpClient == NULL) {
        m_connectionHealthy = false;
        return false;
    }

    bool healthy = m_httpClient.TestConnection();
    UpdateConnectionHealth(healthy);

    return healthy;
}

//+------------------------------------------------------------------+
//| Update connection health status                                 |
//+------------------------------------------------------------------+
void CSmartPollingClient::UpdateConnectionHealth(bool healthy) {
    if(m_connectionHealthy != healthy) {
        m_connectionHealthy = healthy;

        if(m_logger != NULL) {
            m_logger.Info(StringFormat("Connection health changed to: %s",
                         healthy ? "healthy" : "unhealthy"));
        }
    }
}
