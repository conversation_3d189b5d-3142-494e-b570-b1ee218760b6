//+------------------------------------------------------------------+
//| RiskManager.mqh                                                  |
//| Risk management system for Follower EA                          |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include "Logger.mqh"

//+------------------------------------------------------------------+
//| Risk Manager Class                                               |
//+------------------------------------------------------------------+
class CRiskManager {
private:
    CLogger*         m_logger;
    
    // Risk parameters
    double           m_riskPercent;
    double           m_maxLotSize;
    double           m_minLotSize;
    double           m_maxDailyLoss;
    double           m_maxDrawdown;
    double           m_maxRiskPerTrade;
    
    // Account monitoring
    double           m_initialBalance;
    double           m_dailyStartBalance;
    double           m_peakBalance;
    datetime         m_lastResetDate;
    
    // Position tracking
    double           m_totalRiskExposure;
    int              m_openPositionsCount;
    
public:
    //--- Constructor
    CRiskManager(CLogger* logger);
    
    //--- Destructor
    ~CRiskManager();
    
    //--- Main risk methods
    bool CheckRiskLimits();
    double CalculatePositionSize(string symbol, double stopLoss, double baseVolume = 0);
    bool ValidateNewPosition(string symbol, double volume, double stopLoss);
    
    //--- Risk monitoring
    void OnTick();
    void UpdateRiskExposure();
    double GetCurrentDrawdown();
    double GetDailyPnL();
    bool IsMaxDailyLossReached();
    bool IsMaxDrawdownReached();
    
    //--- Configuration
    void SetRiskPercent(double percent) { m_riskPercent = percent; }
    void SetMaxLotSize(double maxLot) { m_maxLotSize = maxLot; }
    void SetMinLotSize(double minLot) { m_minLotSize = minLot; }
    void SetMaxDailyLoss(double maxLoss) { m_maxDailyLoss = maxLoss; }
    void SetMaxDrawdown(double maxDD) { m_maxDrawdown = maxDD; }
    
    //--- Getters
    double GetRiskPercent() { return m_riskPercent; }
    double GetMaxLotSize() { return m_maxLotSize; }
    double GetTotalRiskExposure() { return m_totalRiskExposure; }
    int GetOpenPositionsCount() { return m_openPositionsCount; }
    
private:
    //--- Helper methods
    double CalculateRiskAmount();
    double CalculatePipValue(string symbol);
    double CalculateStopLossDistance(string symbol, double stopLoss, ENUM_POSITION_TYPE posType);
    void ResetDailyCounters();
    void UpdatePeakBalance();
    double NormalizeVolume(string symbol, double volume);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CRiskManager::CRiskManager(CLogger* logger) {
    m_logger = logger;
    
    // Default risk parameters
    m_riskPercent = 2.0;
    m_maxLotSize = 1.0;
    m_minLotSize = 0.01;
    m_maxDailyLoss = 500.0;
    m_maxDrawdown = 20.0;
    m_maxRiskPerTrade = 5.0;
    
    // Initialize account monitoring
    m_initialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    m_dailyStartBalance = m_initialBalance;
    m_peakBalance = m_initialBalance;
    m_lastResetDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Initialize position tracking
    m_totalRiskExposure = 0.0;
    m_openPositionsCount = 0;
    
    UpdateRiskExposure();
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CRiskManager::~CRiskManager() {
    // Cleanup if needed
}

//+------------------------------------------------------------------+
//| Check all risk limits                                            |
//+------------------------------------------------------------------+
bool CRiskManager::CheckRiskLimits() {
    // Check daily loss limit
    if(IsMaxDailyLossReached()) {
        m_logger.Warning("Maximum daily loss reached");
        return false;
    }
    
    // Check drawdown limit
    if(IsMaxDrawdownReached()) {
        m_logger.Warning("Maximum drawdown reached");
        return false;
    }
    
    // Check if account has sufficient margin
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(freeMargin <= 0) {
        m_logger.Warning("Insufficient free margin");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                           |
//+------------------------------------------------------------------+
double CRiskManager::CalculatePositionSize(string symbol, double stopLoss, double baseVolume = 0) {
    // If no stop loss, use base volume or minimum
    if(stopLoss <= 0) {
        if(baseVolume > 0) {
            return NormalizeVolume(symbol, MathMin(baseVolume, m_maxLotSize));
        }
        return m_minLotSize;
    }
    
    // Calculate risk amount
    double riskAmount = CalculateRiskAmount();
    
    // Get current price for calculation
    double currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
    
    // Calculate stop loss distance in pips
    double stopLossDistance = MathAbs(currentPrice - stopLoss);
    
    // Calculate pip value
    double pipValue = CalculatePipValue(symbol);
    
    if(pipValue <= 0) {
        m_logger.Warning(StringFormat("Invalid pip value for %s", symbol));
        return m_minLotSize;
    }
    
    // Calculate position size
    double volume = riskAmount / (stopLossDistance * pipValue * 100000);
    
    // Apply base volume if provided
    if(baseVolume > 0) {
        volume = MathMin(volume, baseVolume);
    }
    
    // Normalize and apply limits
    volume = NormalizeVolume(symbol, volume);
    volume = MathMax(volume, m_minLotSize);
    volume = MathMin(volume, m_maxLotSize);
    
    m_logger.Debug(StringFormat("Position size calculated: %.2f for %s (Risk: %.2f, SL Distance: %.5f)", 
                                volume, symbol, riskAmount, stopLossDistance));
    
    return volume;
}

//+------------------------------------------------------------------+
//| Validate new position                                            |
//+------------------------------------------------------------------+
bool CRiskManager::ValidateNewPosition(string symbol, double volume, double stopLoss) {
    // Check volume limits
    if(volume < m_minLotSize || volume > m_maxLotSize) {
        m_logger.Warning(StringFormat("Volume out of range: %.2f", volume));
        return false;
    }
    
    // Check if adding this position would exceed risk limits
    double positionRisk = 0;
    if(stopLoss > 0) {
        double currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
        double stopLossDistance = MathAbs(currentPrice - stopLoss);
        double pipValue = CalculatePipValue(symbol);
        positionRisk = stopLossDistance * pipValue * volume * 100000;
    }
    
    if(positionRisk > CalculateRiskAmount() * m_maxRiskPerTrade / 100) {
        m_logger.Warning(StringFormat("Position risk too high: %.2f", positionRisk));
        return false;
    }
    
    // Check margin requirement
    double marginRequired = 0;
    if(!OrderCalcMargin(ORDER_TYPE_BUY, symbol, volume, 
                       SymbolInfoDouble(symbol, SYMBOL_ASK), marginRequired)) {
        m_logger.Warning("Failed to calculate margin requirement");
        return false;
    }
    
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(marginRequired > freeMargin * 0.8) { // Use max 80% of free margin
        m_logger.Warning(StringFormat("Insufficient margin. Required: %.2f, Available: %.2f", 
                                     marginRequired, freeMargin));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| On tick processing                                               |
//+------------------------------------------------------------------+
void CRiskManager::OnTick() {
    // Reset daily counters if needed
    datetime currentDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    if(currentDate > m_lastResetDate) {
        ResetDailyCounters();
        m_lastResetDate = currentDate;
    }
    
    // Update peak balance
    UpdatePeakBalance();
    
    // Update risk exposure periodically
    static datetime lastUpdate = 0;
    if(TimeCurrent() - lastUpdate >= 60) { // Update every minute
        UpdateRiskExposure();
        lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Update risk exposure                                             |
//+------------------------------------------------------------------+
void CRiskManager::UpdateRiskExposure() {
    m_totalRiskExposure = 0.0;
    m_openPositionsCount = 0;
    
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            m_openPositionsCount++;
            
            double volume = PositionGetDouble(POSITION_VOLUME);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double stopLoss = PositionGetDouble(POSITION_SL);
            string symbol = PositionGetString(POSITION_SYMBOL);
            
            if(stopLoss > 0) {
                double stopLossDistance = MathAbs(openPrice - stopLoss);
                double pipValue = CalculatePipValue(symbol);
                double positionRisk = stopLossDistance * pipValue * volume * 100000;
                m_totalRiskExposure += positionRisk;
            }
        }
    }
    
    m_logger.Debug(StringFormat("Risk exposure updated: %.2f, Open positions: %d", 
                                m_totalRiskExposure, m_openPositionsCount));
}

//+------------------------------------------------------------------+
//| Get current drawdown                                             |
//+------------------------------------------------------------------+
double CRiskManager::GetCurrentDrawdown() {
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double drawdown = (m_peakBalance - currentEquity) / m_peakBalance * 100;
    return MathMax(0, drawdown);
}

//+------------------------------------------------------------------+
//| Get daily P&L                                                    |
//+------------------------------------------------------------------+
double CRiskManager::GetDailyPnL() {
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    return currentEquity - m_dailyStartBalance;
}

//+------------------------------------------------------------------+
//| Check if maximum daily loss is reached                          |
//+------------------------------------------------------------------+
bool CRiskManager::IsMaxDailyLossReached() {
    double dailyPnL = GetDailyPnL();
    return dailyPnL <= -m_maxDailyLoss;
}

//+------------------------------------------------------------------+
//| Check if maximum drawdown is reached                            |
//+------------------------------------------------------------------+
bool CRiskManager::IsMaxDrawdownReached() {
    double currentDrawdown = GetCurrentDrawdown();
    return currentDrawdown >= m_maxDrawdown;
}

//+------------------------------------------------------------------+
//| Calculate risk amount                                            |
//+------------------------------------------------------------------+
double CRiskManager::CalculateRiskAmount() {
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    return balance * m_riskPercent / 100.0;
}

//+------------------------------------------------------------------+
//| Calculate pip value                                              |
//+------------------------------------------------------------------+
double CRiskManager::CalculatePipValue(string symbol) {
    double tickValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    
    if(tickSize > 0 && point > 0) {
        return tickValue * point / tickSize;
    }
    
    return 0;
}

//+------------------------------------------------------------------+
//| Calculate stop loss distance                                     |
//+------------------------------------------------------------------+
double CRiskManager::CalculateStopLossDistance(string symbol, double stopLoss, ENUM_POSITION_TYPE posType) {
    double currentPrice;
    
    if(posType == POSITION_TYPE_BUY) {
        currentPrice = SymbolInfoDouble(symbol, SYMBOL_ASK);
        return currentPrice - stopLoss;
    } else {
        currentPrice = SymbolInfoDouble(symbol, SYMBOL_BID);
        return stopLoss - currentPrice;
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void CRiskManager::ResetDailyCounters() {
    m_dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    m_logger.Info(StringFormat("Daily counters reset. Starting balance: %.2f", m_dailyStartBalance));
}

//+------------------------------------------------------------------+
//| Update peak balance                                              |
//+------------------------------------------------------------------+
void CRiskManager::UpdatePeakBalance() {
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(currentEquity > m_peakBalance) {
        m_peakBalance = currentEquity;
    }
}

//+------------------------------------------------------------------+
//| Normalize volume                                                 |
//+------------------------------------------------------------------+
double CRiskManager::NormalizeVolume(string symbol, double volume) {
    double minVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double maxVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
    double stepVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
    
    if(stepVolume > 0) {
        volume = NormalizeDouble(MathRound(volume / stepVolume) * stepVolume, 2);
    }
    
    volume = MathMax(volume, minVolume);
    volume = MathMin(volume, maxVolume);
    
    return volume;
}
