//+------------------------------------------------------------------+
//| TradeExecutor.mqh                                                |
//| Trade execution engine for Follower EA                          |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include <Trade\Trade.mqh>
#include "RiskManager.mqh"
#include "Logger.mqh"

//+------------------------------------------------------------------+
//| Execution Result Structure                                       |
//+------------------------------------------------------------------+
struct <PERSON>ecution<PERSON>ult {
    string   execution_id;
    string   signal_id;
    bool     success;
    ulong    ticket;
    double   executed_volume;
    double   executed_price;
    string   error_message;
    int      error_code;
    datetime execution_time;
    
    ExecutionResult() {
        execution_id = "";
        signal_id = "";
        success = false;
        ticket = 0;
        executed_volume = 0.0;
        executed_price = 0.0;
        error_message = "";
        error_code = 0;
        execution_time = 0;
    }
};

//+------------------------------------------------------------------+
//| Trade Executor Class                                             |
//+------------------------------------------------------------------+
class CTradeExecutor {
private:
    CTrade*          m_trade;
    CRiskManager*    m_riskManager;
    CLogger*         m_logger;
    
    // Configuration
    double           m_copyRatio;
    int              m_maxOpenPositions;
    double           m_minFreeMargin;
    bool             m_useStopLoss;
    bool             m_useTakeProfit;
    int              m_slippage;
    int              m_magicNumber;
    
    // Position tracking
    ulong            m_originalTickets[];
    ulong            m_localTickets[];
    string           m_signalIds[];
    
    // Execution queue
    SignalData       m_pendingSignals[];
    
public:
    //--- Constructor
    CTradeExecutor(CRiskManager* riskManager, CLogger* logger);
    
    //--- Destructor
    ~CTradeExecutor();
    
    //--- Main execution methods
    ExecutionResult ExecuteSignal(const SignalData &signal);
    bool ExecuteOpenSignal(const SignalData &signal, ExecutionResult &result);
    bool ExecuteCloseSignal(const SignalData &signal, ExecutionResult &result);
    bool ExecutePartialCloseSignal(const SignalData &signal, ExecutionResult &result);
    bool ExecuteModifySignal(const SignalData &signal, ExecutionResult &result);
    bool ExecutePendingOrderSignal(const SignalData &signal, ExecutionResult &result);
    
    //--- Queue management
    void AddToQueue(const SignalData &signal);
    void ProcessPendingExecutions();
    void OnTick();
    
    //--- Position management
    bool ClosePositionByOriginalTicket(ulong originalTicket);
    bool ModifyPositionByOriginalTicket(ulong originalTicket, double sl, double tp);
    ulong FindLocalTicket(ulong originalTicket);
    void AddTicketMapping(ulong originalTicket, ulong localTicket, string signalId);
    
    //--- Configuration
    void SetCopyRatio(double ratio) { m_copyRatio = ratio; }
    void SetMaxOpenPositions(int max) { m_maxOpenPositions = max; }
    void SetMinFreeMargin(double margin) { m_minFreeMargin = margin; }
    void SetUseStopLoss(bool use) { m_useStopLoss = use; }
    void SetUseTakeProfit(bool use) { m_useTakeProfit = use; }
    void SetSlippage(int slippage) { m_slippage = slippage; }
    void SetMagicNumber(int magic) { m_magicNumber = magic; }
    
private:
    //--- Helper methods
    bool ValidateExecution(const SignalData &signal);
    double CalculateVolume(const SignalData &signal);
    bool CheckMarginRequirement(string symbol, double volume);
    string GenerateExecutionId();
    void CleanupOldMappings();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CTradeExecutor::CTradeExecutor(CRiskManager* riskManager, CLogger* logger) {
    m_riskManager = riskManager;
    m_logger = logger;
    
    // Initialize trade object
    m_trade = new CTrade();
    m_trade.SetExpertMagicNumber(123456); // Default magic number
    m_trade.SetDeviationInPoints(20);
    m_trade.SetTypeFilling(ORDER_FILLING_IOC);
    
    // Default configuration
    m_copyRatio = 1.0;
    m_maxOpenPositions = 10;
    m_minFreeMargin = 100.0;
    m_useStopLoss = true;
    m_useTakeProfit = true;
    m_slippage = 20;
    m_magicNumber = 123456;
    
    // Initialize arrays
    ArrayResize(m_originalTickets, 0);
    ArrayResize(m_localTickets, 0);
    ArrayResize(m_signalIds, 0);
    ArrayResize(m_pendingSignals, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CTradeExecutor::~CTradeExecutor() {
    if(m_trade != NULL) {
        delete m_trade;
    }
    
    ArrayFree(m_originalTickets);
    ArrayFree(m_localTickets);
    ArrayFree(m_signalIds);
    ArrayFree(m_pendingSignals);
}

//+------------------------------------------------------------------+
//| Execute trading signal                                           |
//+------------------------------------------------------------------+
ExecutionResult CTradeExecutor::ExecuteSignal(const SignalData &signal) {
    ExecutionResult result;
    result.signal_id = signal.signal_id;
    result.execution_id = GenerateExecutionId();
    result.execution_time = TimeCurrent();
    
    m_logger.Info(StringFormat("Executing signal: %s, Action: %s", 
                              signal.signal_id, signal.action));
    
    // Validate signal
    if(!ValidateExecution(signal)) {
        result.error_message = "Signal validation failed";
        m_logger.Warning(result.error_message);
        return result;
    }
    
    // Execute based on action
    if(signal.action == "open") {
        result.success = ExecuteOpenSignal(signal, result);
    } else if(signal.action == "close") {
        if(signal.is_partial_close) {
            result.success = ExecutePartialCloseSignal(signal, result);
        } else {
            result.success = ExecuteCloseSignal(signal, result);
        }
    } else if(signal.action == "modify") {
        result.success = ExecuteModifySignal(signal, result);
    } else if(signal.action == "pending") {
        result.success = ExecutePendingOrderSignal(signal, result);
    } else {
        result.error_message = "Unknown signal action: " + signal.action;
        m_logger.Error(result.error_message);
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Execute open signal                                              |
//+------------------------------------------------------------------+
bool CTradeExecutor::ExecuteOpenSignal(const SignalData &signal, ExecutionResult &result) {
    // Check position limits
    if(PositionsTotal() >= m_maxOpenPositions) {
        result.error_message = "Maximum open positions reached";
        return false;
    }
    
    // Calculate volume
    double volume = CalculateVolume(signal);
    if(volume <= 0) {
        result.error_message = "Invalid volume calculated";
        return false;
    }
    
    // Check margin requirement
    if(!CheckMarginRequirement(signal.symbol, volume)) {
        result.error_message = "Insufficient margin";
        return false;
    }
    
    // Prepare order parameters
    double price = 0; // Market price
    double sl = m_useStopLoss ? signal.stop_loss : 0;
    double tp = m_useTakeProfit ? signal.take_profit : 0;
    string comment = StringFormat("Copy: %s", signal.signal_id);
    
    // Execute trade
    bool success = false;
    if(signal.type == "buy") {
        success = m_trade.Buy(volume, signal.symbol, price, sl, tp, comment);
    } else if(signal.type == "sell") {
        success = m_trade.Sell(volume, signal.symbol, price, sl, tp, comment);
    }
    
    if(success) {
        result.ticket = m_trade.ResultOrder();
        result.executed_volume = volume;
        result.executed_price = m_trade.ResultPrice();
        
        // Add ticket mapping
        AddTicketMapping(signal.ticket, result.ticket, signal.signal_id);
        
        m_logger.Info(StringFormat("Trade executed successfully. Ticket: %d, Volume: %.2f", 
                                  result.ticket, volume));
    } else {
        result.error_code = m_trade.ResultRetcode();
        result.error_message = StringFormat("Trade execution failed. Code: %d", result.error_code);
        m_logger.Error(result.error_message);
    }
    
    return success;
}

//+------------------------------------------------------------------+
//| Execute close signal                                             |
//+------------------------------------------------------------------+
bool CTradeExecutor::ExecuteCloseSignal(const SignalData &signal, ExecutionResult &result) {
    ulong localTicket = FindLocalTicket(signal.ticket);
    
    if(localTicket == 0) {
        result.error_message = "Original position not found";
        return false;
    }
    
    if(!PositionSelectByTicket(localTicket)) {
        result.error_message = "Local position not found";
        return false;
    }
    
    // Close position
    if(m_trade.PositionClose(localTicket)) {
        result.ticket = localTicket;
        result.executed_volume = PositionGetDouble(POSITION_VOLUME);
        result.executed_price = m_trade.ResultPrice();
        
        m_logger.Info(StringFormat("Position closed successfully. Ticket: %d", localTicket));
        return true;
    } else {
        result.error_code = m_trade.ResultRetcode();
        result.error_message = StringFormat("Position close failed. Code: %d", result.error_code);
        m_logger.Error(result.error_message);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Execute partial close signal                                    |
//+------------------------------------------------------------------+
bool CTradeExecutor::ExecutePartialCloseSignal(const SignalData &signal, ExecutionResult &result) {
    ulong localTicket = FindLocalTicket(signal.original_ticket);

    if(localTicket == 0) {
        result.error_message = "Original position not found for partial close";
        return false;
    }

    if(!PositionSelectByTicket(localTicket)) {
        result.error_message = "Local position not found for partial close";
        return false;
    }

    double currentVolume = PositionGetDouble(POSITION_VOLUME);
    double closeVolume = CalculatePartialCloseVolume(signal, currentVolume);

    if(closeVolume <= 0) {
        result.error_message = "Invalid partial close volume calculated";
        return false;
    }

    if(closeVolume >= currentVolume) {
        result.error_message = "Partial close volume exceeds current position volume";
        return false;
    }

    // Execute partial close
    if(m_trade.PositionClosePartial(localTicket, closeVolume)) {
        result.ticket = localTicket;
        result.executed_volume = closeVolume;
        result.executed_price = m_trade.ResultPrice();

        m_logger.Info(StringFormat("Position partially closed. Ticket: %d, Volume: %.2f/%.2f",
                                  localTicket, closeVolume, currentVolume));
        return true;
    } else {
        result.error_code = m_trade.ResultRetcode();
        result.error_message = StringFormat("Partial close failed. Code: %d", result.error_code);
        m_logger.Error(result.error_message);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Calculate partial close volume                                  |
//+------------------------------------------------------------------+
double CTradeExecutor::CalculatePartialCloseVolume(const SignalData &signal, double currentVolume) {
    double closeVolume = 0;

    if(signal.partial_close_volume > 0) {
        // Use specified partial close volume, adjusted by copy ratio
        closeVolume = signal.partial_close_volume * m_copyRatio;
    } else if(signal.remaining_volume > 0) {
        // Calculate from remaining volume
        double targetRemainingVolume = signal.remaining_volume * m_copyRatio;
        closeVolume = currentVolume - targetRemainingVolume;
    } else {
        m_logger.Warning("No valid partial close volume information in signal");
        return 0;
    }

    // Ensure volume is within valid range
    string symbol = PositionGetString(POSITION_SYMBOL);
    double minVolume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
    double volumeStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

    // Round to valid volume step
    closeVolume = MathRound(closeVolume / volumeStep) * volumeStep;

    // Ensure minimum volume
    if(closeVolume < minVolume) {
        closeVolume = minVolume;
    }

    // Ensure we don't close more than current volume minus minimum
    double maxCloseVolume = currentVolume - minVolume;
    if(closeVolume > maxCloseVolume) {
        closeVolume = maxCloseVolume;
    }

    return closeVolume;
}

//+------------------------------------------------------------------+
//| Execute pending order signal                                    |
//+------------------------------------------------------------------+
bool CTradeExecutor::ExecutePendingOrderSignal(const SignalData &signal, ExecutionResult &result) {
    // Calculate volume for follower
    double volume = CalculateVolume(signal.volume);

    if(volume <= 0) {
        result.error_message = "Invalid volume for pending order";
        return false;
    }

    // Determine order type based on signal type and current price
    ENUM_ORDER_TYPE orderType;
    double currentPrice = (signal.type == "buy") ?
        SymbolInfoDouble(signal.symbol, SYMBOL_ASK) :
        SymbolInfoDouble(signal.symbol, SYMBOL_BID);

    if(signal.type == "buy") {
        if(signal.open_price > currentPrice) {
            orderType = ORDER_TYPE_BUY_STOP;
        } else {
            orderType = ORDER_TYPE_BUY_LIMIT;
        }
    } else {
        if(signal.open_price < currentPrice) {
            orderType = ORDER_TYPE_SELL_STOP;
        } else {
            orderType = ORDER_TYPE_SELL_LIMIT;
        }
    }

    // Place pending order
    if(m_trade.OrderOpen(signal.symbol, orderType, volume, 0, signal.open_price,
                        signal.stop_loss, signal.take_profit, ORDER_TIME_GTC, 0,
                        "Copy pending order")) {

        result.ticket = m_trade.ResultOrder();
        result.executed_volume = volume;
        result.executed_price = signal.open_price;

        m_logger.Info(StringFormat("Pending order placed. Ticket: %d, Type: %s, Volume: %.2f",
                                  result.ticket, EnumToString(orderType), volume));
        return true;
    } else {
        result.error_code = m_trade.ResultRetcode();
        result.error_message = StringFormat("Failed to place pending order. Code: %d", result.error_code);
        m_logger.Error(result.error_message);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Execute modify signal                                            |
//+------------------------------------------------------------------+
bool CTradeExecutor::ExecuteModifySignal(const SignalData &signal, ExecutionResult &result) {
    ulong localTicket = FindLocalTicket(signal.ticket);
    
    if(localTicket == 0) {
        result.error_message = "Original position not found";
        return false;
    }
    
    if(!PositionSelectByTicket(localTicket)) {
        result.error_message = "Local position not found";
        return false;
    }
    
    double sl = m_useStopLoss ? signal.stop_loss : PositionGetDouble(POSITION_SL);
    double tp = m_useTakeProfit ? signal.take_profit : PositionGetDouble(POSITION_TP);
    
    // Modify position
    if(m_trade.PositionModify(localTicket, sl, tp)) {
        result.ticket = localTicket;
        
        m_logger.Info(StringFormat("Position modified successfully. Ticket: %d", localTicket));
        return true;
    } else {
        result.error_code = m_trade.ResultRetcode();
        result.error_message = StringFormat("Position modify failed. Code: %d", result.error_code);
        m_logger.Error(result.error_message);
        return false;
    }
}

//+------------------------------------------------------------------+
//| Add signal to execution queue                                   |
//+------------------------------------------------------------------+
void CTradeExecutor::AddToQueue(const SignalData &signal) {
    int size = ArraySize(m_pendingSignals);
    ArrayResize(m_pendingSignals, size + 1);
    m_pendingSignals[size] = signal;
    
    m_logger.Debug(StringFormat("Signal added to queue: %s", signal.signal_id));
}

//+------------------------------------------------------------------+
//| Process pending executions                                       |
//+------------------------------------------------------------------+
void CTradeExecutor::ProcessPendingExecutions() {
    if(ArraySize(m_pendingSignals) == 0) return;
    
    // Process one signal per call to avoid blocking
    SignalData signal = m_pendingSignals[0];
    
    // Remove from queue
    for(int i = 0; i < ArraySize(m_pendingSignals) - 1; i++) {
        m_pendingSignals[i] = m_pendingSignals[i + 1];
    }
    ArrayResize(m_pendingSignals, ArraySize(m_pendingSignals) - 1);
    
    // Execute signal
    ExecutionResult result = ExecuteSignal(signal);
    
    // Log result
    if(result.success) {
        m_logger.Info(StringFormat("Queued signal executed: %s", signal.signal_id));
    } else {
        m_logger.Error(StringFormat("Queued signal failed: %s - %s", 
                                   signal.signal_id, result.error_message));
    }
}

//+------------------------------------------------------------------+
//| On tick processing                                               |
//+------------------------------------------------------------------+
void CTradeExecutor::OnTick() {
    // Cleanup old mappings periodically
    static datetime lastCleanup = 0;
    if(TimeCurrent() - lastCleanup > 3600) { // Every hour
        CleanupOldMappings();
        lastCleanup = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Validate execution                                               |
//+------------------------------------------------------------------+
bool CTradeExecutor::ValidateExecution(const SignalData &signal) {
    // Check if symbol is available
    if(!SymbolSelect(signal.symbol, true)) {
        m_logger.Warning(StringFormat("Symbol not available: %s", signal.symbol));
        return false;
    }
    
    // Check market hours
    if(!SymbolInfoInteger(signal.symbol, SYMBOL_TRADE_MODE)) {
        m_logger.Warning(StringFormat("Trading not allowed for symbol: %s", signal.symbol));
        return false;
    }
    
    // Check risk limits
    if(!m_riskManager.CheckRiskLimits()) {
        m_logger.Warning("Risk limits exceeded");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate volume for execution                                   |
//+------------------------------------------------------------------+
double CTradeExecutor::CalculateVolume(const SignalData &signal) {
    // Apply copy ratio
    double baseVolume = signal.volume * m_copyRatio;
    
    // Apply risk management
    double riskAdjustedVolume = m_riskManager.CalculatePositionSize(
        signal.symbol, 
        signal.stop_loss, 
        baseVolume
    );
    
    return riskAdjustedVolume;
}

//+------------------------------------------------------------------+
//| Check margin requirement                                         |
//+------------------------------------------------------------------+
bool CTradeExecutor::CheckMarginRequirement(string symbol, double volume) {
    double marginRequired = 0;
    
    if(!OrderCalcMargin(ORDER_TYPE_BUY, symbol, volume, 
                       SymbolInfoDouble(symbol, SYMBOL_ASK), marginRequired)) {
        return false;
    }
    
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    
    return (freeMargin - marginRequired) >= m_minFreeMargin;
}

//+------------------------------------------------------------------+
//| Find local ticket by original ticket                            |
//+------------------------------------------------------------------+
ulong CTradeExecutor::FindLocalTicket(ulong originalTicket) {
    for(int i = 0; i < ArraySize(m_originalTickets); i++) {
        if(m_originalTickets[i] == originalTicket) {
            return m_localTickets[i];
        }
    }
    return 0;
}

//+------------------------------------------------------------------+
//| Add ticket mapping                                               |
//+------------------------------------------------------------------+
void CTradeExecutor::AddTicketMapping(ulong originalTicket, ulong localTicket, string signalId) {
    int size = ArraySize(m_originalTickets);
    ArrayResize(m_originalTickets, size + 1);
    ArrayResize(m_localTickets, size + 1);
    ArrayResize(m_signalIds, size + 1);
    
    m_originalTickets[size] = originalTicket;
    m_localTickets[size] = localTicket;
    m_signalIds[size] = signalId;
}

//+------------------------------------------------------------------+
//| Generate execution ID                                            |
//+------------------------------------------------------------------+
string CTradeExecutor::GenerateExecutionId() {
    return StringFormat("EXEC_%d_%d_%d", 
                       AccountInfoInteger(ACCOUNT_LOGIN),
                       TimeCurrent(),
                       MathRand());
}

//+------------------------------------------------------------------+
//| Cleanup old mappings                                             |
//+------------------------------------------------------------------+
void CTradeExecutor::CleanupOldMappings() {
    // Remove mappings for closed positions
    for(int i = ArraySize(m_localTickets) - 1; i >= 0; i--) {
        if(!PositionSelectByTicket(m_localTickets[i])) {
            // Position is closed, remove mapping
            for(int j = i; j < ArraySize(m_localTickets) - 1; j++) {
                m_originalTickets[j] = m_originalTickets[j + 1];
                m_localTickets[j] = m_localTickets[j + 1];
                m_signalIds[j] = m_signalIds[j + 1];
            }
            
            ArrayResize(m_originalTickets, ArraySize(m_originalTickets) - 1);
            ArrayResize(m_localTickets, ArraySize(m_localTickets) - 1);
            ArrayResize(m_signalIds, ArraySize(m_signalIds) - 1);
        }
    }
    
    m_logger.Debug(StringFormat("Ticket mappings cleaned up. Active: %d", 
                                ArraySize(m_localTickets)));
}
