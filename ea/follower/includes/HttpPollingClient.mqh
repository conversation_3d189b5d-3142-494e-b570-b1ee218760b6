//+------------------------------------------------------------------+
//| HttpPollingClient.mqh                                            |
//| HTTP Polling Client for MT5 Signal Reception                    |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include "HttpClient.mqh"
#include "Logger.mqh"
#include "../../includes/SignalData.mqh"

//+------------------------------------------------------------------+
//| HTTP Polling Client Class                                        |
//+------------------------------------------------------------------+
class CHttpPollingClient {
private:
    CHttpClient*    m_httpClient;
    CLogger*        m_logger;
    
    // Polling configuration
    string          m_baseUrl;
    string          m_apiKey;
    string          m_followerId;
    int             m_pollInterval;     // seconds
    datetime        m_lastPollTime;
    
    // Connection state
    bool            m_isConnected;
    bool            m_isPolling;
    int             m_consecutiveErrors;
    int             m_maxErrors;
    
    // Message queue
    SignalData      m_messageQueue[];
    int             m_queueSize;
    int             m_maxQueueSize;

public:
    // Constructor
    CHttpPollingClient(string baseUrl, string apiKey, string followerId, CLogger* logger) {
        m_baseUrl = baseUrl;
        m_apiKey = apiKey;
        m_followerId = followerId;
        m_logger = logger;
        
        m_httpClient = new CHttpClient(baseUrl, apiKey, logger);
        
        // Default configuration
        m_pollInterval = 1;         // 1 second
        m_maxErrors = 5;
        m_maxQueueSize = 100;
        
        // Initialize state
        m_isConnected = false;
        m_isPolling = false;
        m_consecutiveErrors = 0;
        m_lastPollTime = 0;
        m_queueSize = 0;
        
        ArrayResize(m_messageQueue, m_maxQueueSize);
    }
    
    // Destructor
    ~CHttpPollingClient() {
        Stop();
        if(m_httpClient != NULL) {
            delete m_httpClient;
            m_httpClient = NULL;
        }
    }
    
    // Start polling
    bool Start() {
        if(m_isPolling) {
            m_logger.Warning("Polling already started");
            return true;
        }
        
        // Test connection first
        if(!TestConnection()) {
            m_logger.Error("Failed to connect to server");
            return false;
        }
        
        m_isPolling = true;
        m_isConnected = true;
        m_consecutiveErrors = 0;
        m_lastPollTime = TimeCurrent();
        
        m_logger.Info("HTTP polling started");
        return true;
    }
    
    // Stop polling
    void Stop() {
        m_isPolling = false;
        m_isConnected = false;
        m_logger.Info("HTTP polling stopped");
    }
    
    // Main polling function - call this in OnTick()
    void Poll() {
        if(!m_isPolling) return;
        
        datetime currentTime = TimeCurrent();
        
        // Check if it's time to poll
        if(currentTime - m_lastPollTime < m_pollInterval) {
            return;
        }
        
        m_lastPollTime = currentTime;
        
        // Poll for new messages
        if(!PollMessages()) {
            HandlePollError();
        } else {
            // Reset error counter on success
            m_consecutiveErrors = 0;
            m_isConnected = true;
        }
    }
    
    // Get next message from queue
    bool GetNextMessage(SignalData &signal) {
        if(m_queueSize <= 0) {
            return false;
        }
        
        // Get first message
        signal = m_messageQueue[0];
        
        // Shift queue
        for(int i = 0; i < m_queueSize - 1; i++) {
            m_messageQueue[i] = m_messageQueue[i + 1];
        }
        
        m_queueSize--;
        return true;
    }
    
    // Check if connected
    bool IsConnected() {
        return m_isConnected && m_consecutiveErrors < m_maxErrors;
    }
    
    // Get queue size
    int GetQueueSize() {
        return m_queueSize;
    }
    
    // Set poll interval
    void SetPollInterval(int seconds) {
        m_pollInterval = MathMax(1, seconds);
    }

private:
    // Test connection to server
    bool TestConnection() {
        string response;
        int statusCode;
        
        string endpoint = "/health";
        
        if(m_httpClient.Get(endpoint, response, statusCode)) {
            return statusCode == 200;
        }
        
        return false;
    }
    
    // Poll for new messages
    bool PollMessages() {
        string response;
        int statusCode;
        
        // Build polling endpoint with follower ID
        string endpoint = StringFormat("/api/v1/mt5/poll?follower_id=%s", m_followerId);
        
        // Add timestamp to prevent caching
        endpoint += StringFormat("&t=%d", TimeCurrent());
        
        if(!m_httpClient.Get(endpoint, response, statusCode)) {
            m_logger.Error("Failed to poll messages - network error");
            return false;
        }
        
        if(statusCode != 200) {
            m_logger.Warning(StringFormat("Poll failed with status: %d", statusCode));
            return false;
        }
        
        // Parse response and add messages to queue
        return ParseAndQueueMessages(response);
    }
    
    // Parse response and add messages to queue
    bool ParseAndQueueMessages(string response) {
        // Simple JSON parsing for signals array
        // In production, you'd want a proper JSON parser
        
        if(StringLen(response) == 0 || response == "[]") {
            // No new messages
            return true;
        }
        
        m_logger.Debug(StringFormat("Received response: %s", response));
        
        // For now, assume single signal per response
        // In production, parse JSON array properly
        SignalData signal;
        if(ParseSignalFromJson(response, signal)) {
            return AddMessageToQueue(signal);
        }
        
        return true;
    }
    
    // Parse single signal from JSON
    bool ParseSignalFromJson(string json, SignalData &signal) {
        // Initialize signal
        CSignalDataHelper::InitializeSignalData(signal);
        
        // Simple JSON parsing - in production use proper JSON parser
        // This is a simplified version for demonstration
        
        // Extract signal_id
        int pos = StringFind(json, "\"signal_id\":\"");
        if(pos >= 0) {
            pos += 13; // length of "signal_id":""
            int endPos = StringFind(json, "\"", pos);
            if(endPos > pos) {
                signal.signal_id = StringSubstr(json, pos, endPos - pos);
            }
        }
        
        // Extract action
        pos = StringFind(json, "\"action\":\"");
        if(pos >= 0) {
            pos += 10; // length of "action":""
            int endPos = StringFind(json, "\"", pos);
            if(endPos > pos) {
                signal.action = StringSubstr(json, pos, endPos - pos);
            }
        }
        
        // Extract symbol
        pos = StringFind(json, "\"symbol\":\"");
        if(pos >= 0) {
            pos += 10; // length of "symbol":""
            int endPos = StringFind(json, "\"", pos);
            if(endPos > pos) {
                signal.symbol = StringSubstr(json, pos, endPos - pos);
            }
        }
        
        // Extract type
        pos = StringFind(json, "\"type\":\"");
        if(pos >= 0) {
            pos += 8; // length of "type":""
            int endPos = StringFind(json, "\"", pos);
            if(endPos > pos) {
                signal.type = StringSubstr(json, pos, endPos - pos);
            }
        }
        
        // Extract volume
        pos = StringFind(json, "\"volume\":");
        if(pos >= 0) {
            pos += 9; // length of "volume":"
            int endPos = StringFind(json, ",", pos);
            if(endPos < 0) endPos = StringFind(json, "}", pos);
            if(endPos > pos) {
                string volumeStr = StringSubstr(json, pos, endPos - pos);
                signal.volume = StringToDouble(volumeStr);
            }
        }
        
        // Extract is_partial_close
        pos = StringFind(json, "\"is_partial_close\":");
        if(pos >= 0) {
            pos += 19; // length of "is_partial_close":"
            signal.is_partial_close = (StringFind(json, "true", pos) == pos);
        }
        
        // Extract partial_close_volume
        pos = StringFind(json, "\"partial_close_volume\":");
        if(pos >= 0) {
            pos += 23; // length of "partial_close_volume":"
            int endPos = StringFind(json, ",", pos);
            if(endPos < 0) endPos = StringFind(json, "}", pos);
            if(endPos > pos) {
                string volumeStr = StringSubstr(json, pos, endPos - pos);
                signal.partial_close_volume = StringToDouble(volumeStr);
            }
        }
        
        // Extract remaining_volume
        pos = StringFind(json, "\"remaining_volume\":");
        if(pos >= 0) {
            pos += 19; // length of "remaining_volume":"
            int endPos = StringFind(json, ",", pos);
            if(endPos < 0) endPos = StringFind(json, "}", pos);
            if(endPos > pos) {
                string volumeStr = StringSubstr(json, pos, endPos - pos);
                signal.remaining_volume = StringToDouble(volumeStr);
            }
        }
        
        // Set timestamp
        signal.timestamp = TimeCurrent();
        
        // Validate parsed signal
        return CSignalDataHelper::ValidateSignal(signal);
    }
    
    // Add message to queue
    bool AddMessageToQueue(const SignalData &signal) {
        if(m_queueSize >= m_maxQueueSize) {
            m_logger.Warning("Message queue full, dropping oldest message");
            
            // Shift queue to make room
            for(int i = 0; i < m_maxQueueSize - 1; i++) {
                m_messageQueue[i] = m_messageQueue[i + 1];
            }
            m_queueSize = m_maxQueueSize - 1;
        }
        
        // Add new message
        m_messageQueue[m_queueSize] = signal;
        m_queueSize++;
        
        m_logger.Debug(StringFormat("Added signal to queue: %s", signal.signal_id));
        return true;
    }
    
    // Handle polling error
    void HandlePollError() {
        m_consecutiveErrors++;
        
        if(m_consecutiveErrors >= m_maxErrors) {
            m_isConnected = false;
            m_logger.Error(StringFormat("Too many consecutive errors (%d), marking as disconnected", 
                                       m_consecutiveErrors));
        }
        
        // Exponential backoff
        int backoffTime = MathMin(60, m_pollInterval * m_consecutiveErrors);
        m_lastPollTime = TimeCurrent() + backoffTime;
        
        m_logger.Warning(StringFormat("Poll error #%d, backing off for %d seconds", 
                                     m_consecutiveErrors, backoffTime));
    }
};
