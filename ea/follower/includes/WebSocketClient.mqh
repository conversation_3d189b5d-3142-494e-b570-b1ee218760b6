//+------------------------------------------------------------------+
//| WebSocketClient.mqh                                              |
//| WebSocket client for receiving real-time signals                |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include "Logger.mqh"

//--- Forward declarations
class CWebSocketClient;

//--- Callback function type
typedef void (*SignalCallback)(string signalJson);

//+------------------------------------------------------------------+
//| WebSocket Client Class                                           |
//+------------------------------------------------------------------+
class CWebSocketClient {
private:
    string           m_url;
    string           m_apiKey;
    CLogger*         m_logger;
    bool             m_connected;
    datetime         m_lastPing;
    datetime         m_lastReconnectAttempt;
    int              m_reconnectInterval;
    SignalCallback   m_signalCallback;
    
    // Since MT5 doesn't have native WebSocket, we'll simulate with HTTP polling
    datetime         m_lastPoll;
    int              m_pollInterval;
    
public:
    //--- Constructor
    CWebSocketClient(string url, string apiKey, CLogger* logger);
    
    //--- Destructor
    ~CWebSocketClient();
    
    //--- Connection methods
    bool Connect();
    void Disconnect();
    bool IsConnected() { return m_connected; }
    
    //--- Message handling
    void SetSignalCallback(SignalCallback callback) { m_signalCallback = callback; }
    void CheckMessages();
    
    //--- Configuration
    void SetReconnectInterval(int seconds) { m_reconnectInterval = seconds; }
    void SetPollInterval(int seconds) { m_pollInterval = seconds; }
    
private:
    //--- Helper methods
    bool SendHandshake();
    bool PollForMessages();
    void ProcessMessage(string message);
    bool SendPing();
    void HandleReconnection();
    string BuildAuthHeaders();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CWebSocketClient::CWebSocketClient(string url, string apiKey, CLogger* logger) {
    m_url = url;
    m_apiKey = apiKey;
    m_logger = logger;
    m_connected = false;
    m_lastPing = 0;
    m_lastReconnectAttempt = 0;
    m_reconnectInterval = 5;
    m_signalCallback = NULL;
    m_lastPoll = 0;
    m_pollInterval = 1; // Poll every second
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CWebSocketClient::~CWebSocketClient() {
    Disconnect();
}

//+------------------------------------------------------------------+
//| Connect to WebSocket server                                      |
//+------------------------------------------------------------------+
bool CWebSocketClient::Connect() {
    m_logger.Info("Connecting to WebSocket server...");
    
    // Since MT5 doesn't support WebSocket natively, we'll use HTTP polling
    // In a real implementation, you might use a DLL or external service
    
    if(SendHandshake()) {
        m_connected = true;
        m_lastPing = TimeCurrent();
        m_logger.Info("WebSocket connection established (HTTP polling mode)");
        return true;
    }
    
    m_logger.Error("Failed to establish WebSocket connection");
    return false;
}

//+------------------------------------------------------------------+
//| Disconnect from WebSocket server                                |
//+------------------------------------------------------------------+
void CWebSocketClient::Disconnect() {
    if(m_connected) {
        m_connected = false;
        m_logger.Info("WebSocket disconnected");
    }
}

//+------------------------------------------------------------------+
//| Send handshake to server                                         |
//+------------------------------------------------------------------+
bool CWebSocketClient::SendHandshake() {
    string endpoint = StringReplace(m_url, "wss://", "https://");
    endpoint = StringReplace(endpoint, "ws://", "http://");
    endpoint += "/connect";
    
    string jsonData = StringFormat(
        "{"
        "\"type\":\"handshake\","
        "\"follower_id\":\"%s\","
        "\"timestamp\":%d"
        "}",
        "FOLLOWER_ID", // This should be passed from main EA
        TimeCurrent()
    );
    
    char postData[];
    char result[];
    string headers = BuildAuthHeaders();
    string resultHeaders;
    
    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));
    
    int statusCode = WebRequest("POST", endpoint, headers, 5000, postData, result, resultHeaders);
    
    if(statusCode == 200) {
        m_logger.Debug("Handshake successful");
        return true;
    }
    
    m_logger.Error(StringFormat("Handshake failed. Status: %d", statusCode));
    return false;
}

//+------------------------------------------------------------------+
//| Check for new messages                                           |
//+------------------------------------------------------------------+
void CWebSocketClient::CheckMessages() {
    if(!m_connected) {
        HandleReconnection();
        return;
    }
    
    // Poll for messages
    if(TimeCurrent() - m_lastPoll >= m_pollInterval) {
        PollForMessages();
        m_lastPoll = TimeCurrent();
    }
    
    // Send ping if needed
    if(TimeCurrent() - m_lastPing >= 30) { // Ping every 30 seconds
        SendPing();
        m_lastPing = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Poll for messages from server                                   |
//+------------------------------------------------------------------+
bool CWebSocketClient::PollForMessages() {
    string endpoint = StringReplace(m_url, "wss://", "https://");
    endpoint = StringReplace(endpoint, "ws://", "http://");
    endpoint += "/poll";
    
    char data[];
    char result[];
    string headers = BuildAuthHeaders();
    string resultHeaders;
    
    int statusCode = WebRequest("GET", endpoint, headers, 5000, data, result, resultHeaders);
    
    if(statusCode == 200) {
        string response = CharArrayToString(result);
        
        if(StringLen(response) > 0 && response != "[]") {
            ProcessMessage(response);
        }
        
        return true;
    } else if(statusCode == 204) {
        // No new messages
        return true;
    } else {
        m_logger.Warning(StringFormat("Poll failed. Status: %d", statusCode));
        return false;
    }
}

//+------------------------------------------------------------------+
//| Process received message                                         |
//+------------------------------------------------------------------+
void CWebSocketClient::ProcessMessage(string message) {
    m_logger.Debug(StringFormat("Received message: %s", message));
    
    // Parse JSON array of messages
    if(StringFind(message, "[") == 0) {
        // Multiple messages
        string messages[];
        ParseJsonArray(message, messages);
        
        for(int i = 0; i < ArraySize(messages); i++) {
            if(m_signalCallback != NULL) {
                m_signalCallback(messages[i]);
            }
        }
    } else {
        // Single message
        if(m_signalCallback != NULL) {
            m_signalCallback(message);
        }
    }
}

//+------------------------------------------------------------------+
//| Send ping to server                                              |
//+------------------------------------------------------------------+
bool CWebSocketClient::SendPing() {
    string endpoint = StringReplace(m_url, "wss://", "https://");
    endpoint = StringReplace(endpoint, "ws://", "http://");
    endpoint += "/ping";
    
    char data[];
    char result[];
    string headers = BuildAuthHeaders();
    string resultHeaders;
    
    int statusCode = WebRequest("GET", endpoint, headers, 3000, data, result, resultHeaders);
    
    if(statusCode == 200) {
        m_logger.Debug("Ping successful");
        return true;
    }
    
    m_logger.Warning(StringFormat("Ping failed. Status: %d", statusCode));
    return false;
}

//+------------------------------------------------------------------+
//| Handle reconnection                                              |
//+------------------------------------------------------------------+
void CWebSocketClient::HandleReconnection() {
    if(TimeCurrent() - m_lastReconnectAttempt < m_reconnectInterval) {
        return;
    }
    
    m_logger.Info("Attempting to reconnect...");
    m_lastReconnectAttempt = TimeCurrent();
    
    if(Connect()) {
        m_logger.Info("Reconnection successful");
    } else {
        m_logger.Warning("Reconnection failed, will retry");
    }
}

//+------------------------------------------------------------------+
//| Build authentication headers                                     |
//+------------------------------------------------------------------+
string CWebSocketClient::BuildAuthHeaders() {
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + m_apiKey + "\r\n";
    headers += "User-Agent: MT5-FollowerEA/2.0\r\n";
    return headers;
}

//+------------------------------------------------------------------+
//| Parse JSON array (simplified)                                   |
//+------------------------------------------------------------------+
void ParseJsonArray(string jsonArray, string &messages[]) {
    // Remove brackets
    string content = StringSubstr(jsonArray, 1, StringLen(jsonArray) - 2);
    
    // Split by objects
    ArrayResize(messages, 0);
    int count = 0;
    int start = 0;
    int braceCount = 0;
    
    for(int i = 0; i < StringLen(content); i++) {
        ushort ch = StringGetCharacter(content, i);
        
        if(ch == '{') {
            braceCount++;
        } else if(ch == '}') {
            braceCount--;
            
            if(braceCount == 0) {
                string obj = StringSubstr(content, start, i - start + 1);
                ArrayResize(messages, count + 1);
                messages[count] = obj;
                count++;
                
                // Find next object start
                for(int j = i + 1; j < StringLen(content); j++) {
                    if(StringGetCharacter(content, j) == '{') {
                        start = j;
                        i = j - 1; // Will be incremented by loop
                        break;
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Signal Data Parser                                               |
//+------------------------------------------------------------------+
struct SignalData {
    string   signal_id;
    string   provider_id;
    string   action;
    ulong    ticket;
    string   symbol;
    string   type;
    double   volume;
    double   open_price;
    double   stop_loss;
    double   take_profit;
    string   comment;
    datetime timestamp;
    
    // Parse from JSON
    bool FromJson(string json) {
        signal_id = ExtractJsonString(json, "signal_id");
        provider_id = ExtractJsonString(json, "provider_id");
        action = ExtractJsonString(json, "action");
        ticket = (ulong)ExtractJsonNumber(json, "ticket");
        symbol = ExtractJsonString(json, "symbol");
        type = ExtractJsonString(json, "type");
        volume = ExtractJsonNumber(json, "volume");
        open_price = ExtractJsonNumber(json, "open_price");
        stop_loss = ExtractJsonNumber(json, "stop_loss");
        take_profit = ExtractJsonNumber(json, "take_profit");
        comment = ExtractJsonString(json, "comment");
        timestamp = (datetime)ExtractJsonNumber(json, "timestamp");
        
        return StringLen(signal_id) > 0;
    }
};

//+------------------------------------------------------------------+
//| JSON parsing helpers                                             |
//+------------------------------------------------------------------+
string ExtractJsonString(string json, string key) {
    string searchKey = "\"" + key + "\":\"";
    int startPos = StringFind(json, searchKey);
    if(startPos < 0) return "";
    
    startPos += StringLen(searchKey);
    int endPos = StringFind(json, "\"", startPos);
    
    if(endPos < 0) return "";
    
    return StringSubstr(json, startPos, endPos - startPos);
}

double ExtractJsonNumber(string json, string key) {
    string searchKey = "\"" + key + "\":";
    int startPos = StringFind(json, searchKey);
    if(startPos < 0) return 0.0;
    
    startPos += StringLen(searchKey);
    
    // Skip whitespace
    while(startPos < StringLen(json) && StringGetCharacter(json, startPos) == ' ') {
        startPos++;
    }
    
    int endPos = startPos;
    while(endPos < StringLen(json)) {
        ushort ch = StringGetCharacter(json, endPos);
        if(ch == ',' || ch == '}' || ch == ' ' || ch == '\n' || ch == '\r') {
            break;
        }
        endPos++;
    }
    
    string numberStr = StringSubstr(json, startPos, endPos - startPos);
    return StringToDouble(numberStr);
}
