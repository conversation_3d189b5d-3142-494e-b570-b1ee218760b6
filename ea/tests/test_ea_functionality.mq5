//+------------------------------------------------------------------+
//| test_ea_functionality.mq5                                        |
//| Test script for Copy Trading EA functionality                   |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"
#property script_show_inputs

#include "../provider/includes/HttpClient.mqh"
#include "../provider/includes/SignalManager.mqh"
#include "../provider/includes/Logger.mqh"

//--- Input parameters
input string TEST_API_URL = "http://localhost:8000";
input string TEST_API_KEY = "test-api-key";
input string TEST_PROVIDER_ID = "test-provider-123";
input bool   RUN_ALL_TESTS = true;
input bool   TEST_HTTP_CLIENT = true;
input bool   TEST_SIGNAL_MANAGER = true;
input bool   TEST_LOGGER = true;
input bool   TEST_API_ENDPOINTS = true;

//--- Global variables
CHttpClient* httpClient;
CSignalManager* signalManager;
CLogger* logger;

int totalTests = 0;
int passedTests = 0;
int failedTests = 0;

//+------------------------------------------------------------------+
//| Script program start function                                   |
//+------------------------------------------------------------------+
void OnStart() {
    Print("=== Copy Trading EA Test Suite ===");
    Print("Starting comprehensive EA testing...");
    
    // Initialize components
    InitializeTestComponents();
    
    // Run test suites
    if(RUN_ALL_TESTS || TEST_LOGGER) {
        TestLogger();
    }
    
    if(RUN_ALL_TESTS || TEST_HTTP_CLIENT) {
        TestHttpClient();
    }
    
    if(RUN_ALL_TESTS || TEST_SIGNAL_MANAGER) {
        TestSignalManager();
    }
    
    if(RUN_ALL_TESTS || TEST_API_ENDPOINTS) {
        TestApiEndpoints();
    }
    
    // Performance tests
    TestPerformance();
    
    // Cleanup
    CleanupTestComponents();
    
    // Print results
    PrintTestResults();
}

//+------------------------------------------------------------------+
//| Initialize test components                                       |
//+------------------------------------------------------------------+
void InitializeTestComponents() {
    logger = new CLogger(true, LOG_LEVEL_DEBUG);
    httpClient = new CHttpClient(TEST_API_URL, TEST_API_KEY, logger);
    signalManager = new CSignalManager(httpClient, logger);
    
    logger.Info("Test components initialized");
}

//+------------------------------------------------------------------+
//| Test Logger functionality                                        |
//+------------------------------------------------------------------+
void TestLogger() {
    Print("--- Testing Logger ---");
    
    // Test 1: Basic logging
    RunTest("Logger Basic Logging", TestLoggerBasicLogging());
    
    // Test 2: Log levels
    RunTest("Logger Log Levels", TestLoggerLevels());
    
    // Test 3: File logging
    RunTest("Logger File Logging", TestLoggerFileLogging());
    
    // Test 4: Configuration
    RunTest("Logger Configuration", TestLoggerConfiguration());
}

bool TestLoggerBasicLogging() {
    if(logger == NULL) return false;
    
    logger.Debug("Test debug message");
    logger.Info("Test info message");
    logger.Warning("Test warning message");
    logger.Error("Test error message");
    
    return true;
}

bool TestLoggerLevels() {
    if(logger == NULL) return false;
    
    // Test different log levels
    logger.SetLogLevel(LOG_LEVEL_WARNING);
    logger.Debug("This should not appear");
    logger.Warning("This should appear");
    
    logger.SetLogLevel(LOG_LEVEL_DEBUG);
    logger.Debug("This should appear now");
    
    return true;
}

bool TestLoggerFileLogging() {
    if(logger == NULL) return false;
    
    logger.SetLogToFile(true, "test_log.txt");
    logger.Info("Test file logging");
    
    // Check if file exists
    return FileIsExist("test_log.txt");
}

bool TestLoggerConfiguration() {
    if(logger == NULL) return false;
    
    logger.SetEnabled(false);
    logger.Info("This should not be logged");
    
    logger.SetEnabled(true);
    logger.Info("This should be logged");
    
    return true;
}

//+------------------------------------------------------------------+
//| Test HTTP Client functionality                                  |
//+------------------------------------------------------------------+
void TestHttpClient() {
    Print("--- Testing HTTP Client ---");
    
    // Test 1: Basic GET request
    RunTest("HTTP GET Request", TestHttpGet());
    
    // Test 2: POST request
    RunTest("HTTP POST Request", TestHttpPost());
    
    // Test 3: Authentication
    RunTest("HTTP Authentication", TestHttpAuth());
    
    // Test 4: Error handling
    RunTest("HTTP Error Handling", TestHttpErrorHandling());
    
    // Test 5: Timeout handling
    RunTest("HTTP Timeout", TestHttpTimeout());
}

bool TestHttpGet() {
    if(httpClient == NULL) return false;
    
    string response;
    int statusCode;
    
    bool result = httpClient.Get("/health", response, statusCode);
    
    logger.Info(StringFormat("GET /health - Status: %d, Response: %s", statusCode, response));
    
    return result && statusCode == 200;
}

bool TestHttpPost() {
    if(httpClient == NULL) return false;
    
    string testData = "{\"test\": \"data\"}";
    string response;
    int statusCode;
    
    bool result = httpClient.Post("/api/v1/test", testData, response, statusCode);
    
    logger.Info(StringFormat("POST /api/v1/test - Status: %d", statusCode));
    
    return result;
}

bool TestHttpAuth() {
    if(httpClient == NULL) return false;
    
    string response;
    int statusCode;
    
    // Test authenticated endpoint
    bool result = httpClient.Get("/api/v1/auth/me", response, statusCode);
    
    logger.Info(StringFormat("GET /api/v1/auth/me - Status: %d", statusCode));
    
    // Should return 401 if not authenticated, which is expected
    return statusCode == 401 || statusCode == 200;
}

bool TestHttpErrorHandling() {
    if(httpClient == NULL) return false;
    
    string response;
    int statusCode;
    
    // Test non-existent endpoint
    bool result = httpClient.Get("/api/v1/nonexistent", response, statusCode);
    
    logger.Info(StringFormat("GET /api/v1/nonexistent - Status: %d", statusCode));
    
    return statusCode == 404;
}

bool TestHttpTimeout() {
    if(httpClient == NULL) return false;
    
    // Set short timeout
    httpClient.SetTimeout(1000); // 1 second
    
    string response;
    int statusCode;
    
    // This should timeout or complete quickly
    bool result = httpClient.Get("/health", response, statusCode);
    
    logger.Info(StringFormat("Timeout test - Result: %s, Status: %d", result ? "true" : "false", statusCode));
    
    // Reset timeout
    httpClient.SetTimeout(5000);
    
    return true; // Test passes if no crash occurs
}

//+------------------------------------------------------------------+
//| Test Signal Manager functionality                               |
//+------------------------------------------------------------------+
void TestSignalManager() {
    Print("--- Testing Signal Manager ---");
    
    // Test 1: Signal creation
    RunTest("Signal Creation", TestSignalCreation());
    
    // Test 2: Signal validation
    RunTest("Signal Validation", TestSignalValidation());
    
    // Test 3: Signal sending
    RunTest("Signal Sending", TestSignalSending());
}

bool TestSignalCreation() {
    if(signalManager == NULL) return false;
    
    SignalData signal;
    
    // Create test signal from current position (if any)
    if(PositionsTotal() > 0) {
        PositionSelectByIndex(0);
        return signalManager.CreateSignalFromPosition(signal);
    } else {
        // Create manual test signal
        signal.provider_id = TEST_PROVIDER_ID;
        signal.action = "open";
        signal.symbol = "EURUSD";
        signal.type = "buy";
        signal.volume = 0.1;
        signal.open_price = 1.1234;
        signal.stop_loss = 1.1200;
        signal.take_profit = 1.1300;
        signal.comment = "Test signal";
        signal.timestamp = TimeCurrent();
        
        return true;
    }
}

bool TestSignalValidation() {
    if(signalManager == NULL) return false;
    
    SignalData validSignal;
    validSignal.provider_id = TEST_PROVIDER_ID;
    validSignal.action = "open";
    validSignal.symbol = "EURUSD";
    validSignal.type = "buy";
    validSignal.volume = 0.1;
    
    SignalData invalidSignal;
    invalidSignal.provider_id = "";
    invalidSignal.action = "invalid";
    invalidSignal.symbol = "";
    invalidSignal.volume = -1;
    
    bool validResult = signalManager.ValidateSignal(validSignal);
    bool invalidResult = !signalManager.ValidateSignal(invalidSignal);
    
    return validResult && invalidResult;
}

bool TestSignalSending() {
    if(signalManager == NULL) return false;
    
    SignalData signal;
    signal.provider_id = TEST_PROVIDER_ID;
    signal.action = "open";
    signal.symbol = "EURUSD";
    signal.type = "buy";
    signal.volume = 0.1;
    signal.open_price = 1.1234;
    signal.comment = "Test signal send";
    signal.timestamp = TimeCurrent();
    
    // This will likely fail in test environment, but should not crash
    bool result = signalManager.SendSignal(signal);
    
    logger.Info(StringFormat("Signal send test - Result: %s", result ? "success" : "failed"));
    
    return true; // Test passes if no crash occurs
}

//+------------------------------------------------------------------+
//| Test API endpoints                                               |
//+------------------------------------------------------------------+
void TestApiEndpoints() {
    Print("--- Testing API Endpoints ---");
    
    // Test 1: Health endpoint
    RunTest("Health Endpoint", TestHealthEndpoint());
    
    // Test 2: Provider registration
    RunTest("Provider Registration", TestProviderRegistration());
    
    // Test 3: Heartbeat
    RunTest("Heartbeat", TestHeartbeat());
}

bool TestHealthEndpoint() {
    if(httpClient == NULL) return false;
    
    string response;
    int statusCode;
    
    bool result = httpClient.Get("/health", response, statusCode);
    
    return result && statusCode == 200;
}

bool TestProviderRegistration() {
    if(httpClient == NULL) return false;
    
    string registrationData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"account_number\":%d,"
        "\"broker\":\"%s\","
        "\"server\":\"%s\","
        "\"balance\":%.2f"
        "}",
        TEST_PROVIDER_ID,
        AccountInfoInteger(ACCOUNT_LOGIN),
        AccountInfoString(ACCOUNT_COMPANY),
        AccountInfoString(ACCOUNT_SERVER),
        AccountInfoDouble(ACCOUNT_BALANCE)
    );
    
    string response;
    int statusCode;
    
    bool result = httpClient.Post("/api/v1/providers/register", registrationData, response, statusCode);
    
    logger.Info(StringFormat("Provider registration - Status: %d", statusCode));
    
    return result;
}

bool TestHeartbeat() {
    if(httpClient == NULL) return false;
    
    string heartbeatData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"status\":\"online\","
        "\"timestamp\":%d"
        "}",
        TEST_PROVIDER_ID,
        TimeCurrent()
    );
    
    string response;
    int statusCode;
    
    bool result = httpClient.Post("/api/v1/providers/heartbeat", heartbeatData, response, statusCode);
    
    logger.Info(StringFormat("Heartbeat - Status: %d", statusCode));
    
    return result;
}

//+------------------------------------------------------------------+
//| Test performance                                                 |
//+------------------------------------------------------------------+
void TestPerformance() {
    Print("--- Testing Performance ---");
    
    // Test 1: HTTP request performance
    RunTest("HTTP Performance", TestHttpPerformance());
    
    // Test 2: Signal processing performance
    RunTest("Signal Processing Performance", TestSignalPerformance());
}

bool TestHttpPerformance() {
    if(httpClient == NULL) return false;
    
    int iterations = 10;
    uint startTime = GetTickCount();
    
    for(int i = 0; i < iterations; i++) {
        string response;
        int statusCode;
        httpClient.Get("/health", response, statusCode);
    }
    
    uint endTime = GetTickCount();
    uint totalTime = endTime - startTime;
    double avgTime = (double)totalTime / iterations;
    
    logger.Info(StringFormat("HTTP Performance: %d requests in %d ms (avg: %.2f ms)", 
                            iterations, totalTime, avgTime));
    
    return avgTime < 1000; // Should average less than 1 second per request
}

bool TestSignalPerformance() {
    if(signalManager == NULL) return false;
    
    int iterations = 100;
    uint startTime = GetTickCount();
    
    for(int i = 0; i < iterations; i++) {
        SignalData signal;
        signal.provider_id = TEST_PROVIDER_ID;
        signal.action = "open";
        signal.symbol = "EURUSD";
        signal.type = "buy";
        signal.volume = 0.1;
        
        signalManager.ValidateSignal(signal);
    }
    
    uint endTime = GetTickCount();
    uint totalTime = endTime - startTime;
    double avgTime = (double)totalTime / iterations;
    
    logger.Info(StringFormat("Signal Performance: %d validations in %d ms (avg: %.2f ms)", 
                            iterations, totalTime, avgTime));
    
    return avgTime < 10; // Should average less than 10ms per validation
}

//+------------------------------------------------------------------+
//| Helper functions                                                 |
//+------------------------------------------------------------------+
void RunTest(string testName, bool result) {
    totalTests++;
    
    if(result) {
        passedTests++;
        Print(StringFormat("✓ PASS: %s", testName));
        logger.Info(StringFormat("TEST PASSED: %s", testName));
    } else {
        failedTests++;
        Print(StringFormat("✗ FAIL: %s", testName));
        logger.Error(StringFormat("TEST FAILED: %s", testName));
    }
}

void PrintTestResults() {
    Print("=== Test Results ===");
    Print(StringFormat("Total Tests: %d", totalTests));
    Print(StringFormat("Passed: %d", passedTests));
    Print(StringFormat("Failed: %d", failedTests));
    Print(StringFormat("Success Rate: %.1f%%", (double)passedTests / totalTests * 100));
    
    if(failedTests == 0) {
        Print("🎉 All tests passed!");
    } else {
        Print("⚠️ Some tests failed. Check logs for details.");
    }
}

void CleanupTestComponents() {
    if(httpClient != NULL) {
        delete httpClient;
        httpClient = NULL;
    }
    
    if(signalManager != NULL) {
        delete signalManager;
        signalManager = NULL;
    }
    
    if(logger != NULL) {
        logger.Info("Test cleanup completed");
        delete logger;
        logger = NULL;
    }
    
    // Clean up test files
    if(FileIsExist("test_log.txt")) {
        FileDelete("test_log.txt");
    }
}
