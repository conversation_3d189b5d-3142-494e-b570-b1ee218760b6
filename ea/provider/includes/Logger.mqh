//+------------------------------------------------------------------+
//| Logger.mqh                                                       |
//| Logging utility for MT5 EA                                      |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include <Files\FileTxt.mqh>

//+------------------------------------------------------------------+
//| Log Level Enumeration                                            |
//+------------------------------------------------------------------+
enum ENUM_LOG_LEVEL {
    LOG_LEVEL_DEBUG = 0,
    LOG_LEVEL_INFO = 1,
    LOG_LEVEL_WARNING = 2,
    LOG_LEVEL_ERROR = 3,
    LOG_LEVEL_CRITICAL = 4
};

//+------------------------------------------------------------------+
//| Logger Class                                                     |
//+------------------------------------------------------------------+
class CLogger {
private:
    bool             m_enabled;
    ENUM_LOG_LEVEL   m_logLevel;
    string           m_logFile;
    CFileTxt         m_file;
    bool             m_logToFile;
    bool             m_logToConsole;
    int              m_maxFileSize;
    string           m_dateFormat;
    
public:
    //--- Constructor
    CLogger(bool enabled = true, ENUM_LOG_LEVEL level = LOG_LEVEL_INFO);
    
    //--- Destructor
    ~CLogger();
    
    //--- Logging methods
    void Debug(string message);
    void Info(string message);
    void Warning(string message);
    void Error(string message);
    void Critical(string message);
    
    //--- Configuration
    void SetEnabled(bool enabled) { m_enabled = enabled; }
    void SetLogLevel(ENUM_LOG_LEVEL level) { m_logLevel = level; }
    void SetLogToFile(bool enable, string filename = "");
    void SetLogToConsole(bool enable) { m_logToConsole = enable; }
    void SetMaxFileSize(int maxSize) { m_maxFileSize = maxSize; }
    
private:
    //--- Helper methods
    void WriteLog(ENUM_LOG_LEVEL level, string message);
    string GetLevelString(ENUM_LOG_LEVEL level);
    string GetTimestamp();
    void RotateLogFile();
    bool ShouldLog(ENUM_LOG_LEVEL level);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CLogger::CLogger(bool enabled = true, ENUM_LOG_LEVEL level = LOG_LEVEL_INFO) {
    m_enabled = enabled;
    m_logLevel = level;
    m_logToFile = true;
    m_logToConsole = true;
    m_maxFileSize = 10 * 1024 * 1024; // 10MB
    m_dateFormat = "%Y-%m-%d %H:%M:%S";
    
    // Set default log file
    string accountStr = IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN));
    m_logFile = StringFormat("CopyTrade_Provider_%s_%s.log", 
                            accountStr, 
                            TimeToString(TimeCurrent(), TIME_DATE));
    StringReplace(m_logFile, ".", "_");
    StringReplace(m_logFile, ":", "_");
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CLogger::~CLogger() {
    if(m_file.IsOpen()) {
        m_file.Close();
    }
}

//+------------------------------------------------------------------+
//| Debug level logging                                              |
//+------------------------------------------------------------------+
void CLogger::Debug(string message) {
    WriteLog(LOG_LEVEL_DEBUG, message);
}

//+------------------------------------------------------------------+
//| Info level logging                                               |
//+------------------------------------------------------------------+
void CLogger::Info(string message) {
    WriteLog(LOG_LEVEL_INFO, message);
}

//+------------------------------------------------------------------+
//| Warning level logging                                            |
//+------------------------------------------------------------------+
void CLogger::Warning(string message) {
    WriteLog(LOG_LEVEL_WARNING, message);
}

//+------------------------------------------------------------------+
//| Error level logging                                              |
//+------------------------------------------------------------------+
void CLogger::Error(string message) {
    WriteLog(LOG_LEVEL_ERROR, message);
}

//+------------------------------------------------------------------+
//| Critical level logging                                           |
//+------------------------------------------------------------------+
void CLogger::Critical(string message) {
    WriteLog(LOG_LEVEL_CRITICAL, message);
}

//+------------------------------------------------------------------+
//| Set log to file configuration                                   |
//+------------------------------------------------------------------+
void CLogger::SetLogToFile(bool enable, string filename = "") {
    m_logToFile = enable;
    
    if(StringLen(filename) > 0) {
        m_logFile = filename;
    }
    
    if(m_file.IsOpen()) {
        m_file.Close();
    }
}

//+------------------------------------------------------------------+
//| Write log entry                                                  |
//+------------------------------------------------------------------+
void CLogger::WriteLog(ENUM_LOG_LEVEL level, string message) {
    if(!m_enabled || !ShouldLog(level)) {
        return;
    }
    
    string timestamp = GetTimestamp();
    string levelStr = GetLevelString(level);
    string logEntry = StringFormat("[%s] [%s] %s", timestamp, levelStr, message);
    
    // Log to console
    if(m_logToConsole) {
        Print(logEntry);
    }
    
    // Log to file
    if(m_logToFile) {
        WriteToFile(logEntry);
    }
}

//+------------------------------------------------------------------+
//| Write to log file                                                |
//+------------------------------------------------------------------+
void CLogger::WriteToFile(string logEntry) {
    // Check if file needs rotation
    if(FileSize(m_logFile) > m_maxFileSize) {
        RotateLogFile();
    }
    
    // Open file if not already open
    if(!m_file.IsOpen()) {
        if(!m_file.Open(m_logFile, FILE_WRITE | FILE_READ | FILE_TXT)) {
            Print("Failed to open log file: ", m_logFile);
            return;
        }
        
        // Move to end of file
        m_file.Seek(0, SEEK_END);
    }
    
    // Write log entry
    m_file.WriteString(logEntry + "\n");
    m_file.Flush();
}

//+------------------------------------------------------------------+
//| Get level string                                                 |
//+------------------------------------------------------------------+
string CLogger::GetLevelString(ENUM_LOG_LEVEL level) {
    switch(level) {
        case LOG_LEVEL_DEBUG:    return "DEBUG";
        case LOG_LEVEL_INFO:     return "INFO";
        case LOG_LEVEL_WARNING:  return "WARN";
        case LOG_LEVEL_ERROR:    return "ERROR";
        case LOG_LEVEL_CRITICAL: return "CRITICAL";
        default:                 return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Get formatted timestamp                                          |
//+------------------------------------------------------------------+
string CLogger::GetTimestamp() {
    return TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
}

//+------------------------------------------------------------------+
//| Rotate log file                                                  |
//+------------------------------------------------------------------+
void CLogger::RotateLogFile() {
    if(m_file.IsOpen()) {
        m_file.Close();
    }
    
    // Create backup filename
    string backupFile = m_logFile + ".bak";
    
    // Delete old backup if exists
    if(FileIsExist(backupFile)) {
        FileDelete(backupFile);
    }
    
    // Move current log to backup
    if(FileIsExist(m_logFile)) {
        FileMove(m_logFile, 0, backupFile, 0);
    }
    
    Info("Log file rotated");
}

//+------------------------------------------------------------------+
//| Check if should log at this level                               |
//+------------------------------------------------------------------+
bool CLogger::ShouldLog(ENUM_LOG_LEVEL level) {
    return level >= m_logLevel;
}

//+------------------------------------------------------------------+
//| Config Manager Class                                             |
//+------------------------------------------------------------------+
class CConfigManager {
private:
    string m_configFile;
    
public:
    CConfigManager();
    ~CConfigManager();
    
    bool LoadConfig();
    bool SaveConfig();
    
    // Configuration getters/setters
    string GetApiUrl();
    void SetApiUrl(string url);
    
    string GetApiKey();
    void SetApiKey(string key);
    
    bool GetAutoSendSignals();
    void SetAutoSendSignals(bool enable);
    
private:
    string ReadConfigValue(string key, string defaultValue = "");
    void WriteConfigValue(string key, string value);
};

//+------------------------------------------------------------------+
//| Config Manager Constructor                                       |
//+------------------------------------------------------------------+
CConfigManager::CConfigManager() {
    string accountStr = IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN));
    m_configFile = StringFormat("CopyTrade_Provider_%s.ini", accountStr);
}

//+------------------------------------------------------------------+
//| Config Manager Destructor                                       |
//+------------------------------------------------------------------+
CConfigManager::~CConfigManager() {
    // Cleanup if needed
}

//+------------------------------------------------------------------+
//| Load configuration from file                                    |
//+------------------------------------------------------------------+
bool CConfigManager::LoadConfig() {
    if(!FileIsExist(m_configFile)) {
        Print("Config file not found, using defaults: ", m_configFile);
        return false;
    }
    
    // Implementation would read INI file format
    // For simplicity, returning true here
    return true;
}

//+------------------------------------------------------------------+
//| Save configuration to file                                      |
//+------------------------------------------------------------------+
bool CConfigManager::SaveConfig() {
    // Implementation would write INI file format
    return true;
}

//+------------------------------------------------------------------+
//| Read config value                                                |
//+------------------------------------------------------------------+
string CConfigManager::ReadConfigValue(string key, string defaultValue = "") {
    // Implementation would parse INI file
    return defaultValue;
}

//+------------------------------------------------------------------+
//| Write config value                                               |
//+------------------------------------------------------------------+
void CConfigManager::WriteConfigValue(string key, string value) {
    // Implementation would update INI file
}
