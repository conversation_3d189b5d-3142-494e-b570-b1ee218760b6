//+------------------------------------------------------------------+
//| SignalManager.mqh                                                |
//| Signal management and processing for Provider EA                |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include "HttpClient.mqh"
#include "Logger.mqh"

//+------------------------------------------------------------------+
//| Signal Manager Class                                             |
//+------------------------------------------------------------------+
class CSignalManager {
private:
    CHttpClient* m_httpClient;
    CLogger*     m_logger;
    
    // Signal tracking
    ulong        m_lastProcessedTickets[];
    datetime     m_lastSignalTime;
    int          m_pendingSignalsCount;
    
    // Configuration
    bool         m_validateSignals;
    double       m_minVolume;
    double       m_maxVolume;
    int          m_minProfitPoints;
    
public:
    //--- Constructor
    CSignalManager(CHttpClient* httpClient, CLogger* logger);
    
    //--- Destructor
    ~CSignalManager();
    
    //--- Main methods
    bool SendSignal(SignalData &signal);
    void OnTick();
    void ProcessPendingSignals();
    
    //--- Signal creation
    bool CreateSignalFromPosition(SignalData &signal);
    bool CreateCloseSignal(ulong ticket, SignalData &signal);
    bool CreateModifySignal(ulong ticket, SignalData &signal);
    
    //--- Validation
    bool ValidateSignal(const SignalData &signal);
    bool IsSignalAlreadyProcessed(ulong ticket);
    
    //--- Configuration
    void SetValidation(bool enable) { m_validateSignals = enable; }
    void SetVolumeRange(double minVol, double maxVol) { m_minVolume = minVol; m_maxVolume = maxVol; }
    void SetMinProfitPoints(int points) { m_minProfitPoints = points; }
    
private:
    //--- Helper methods
    string GenerateSignalId();
    void AddProcessedTicket(ulong ticket);
    bool IsTicketProcessed(ulong ticket);
    void CleanupOldTickets();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CSignalManager::CSignalManager(CHttpClient* httpClient, CLogger* logger) {
    m_httpClient = httpClient;
    m_logger = logger;
    m_lastSignalTime = 0;
    m_pendingSignalsCount = 0;
    m_validateSignals = true;
    m_minVolume = 0.01;
    m_maxVolume = 10.0;
    m_minProfitPoints = 10;
    
    ArrayResize(m_lastProcessedTickets, 0);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CSignalManager::~CSignalManager() {
    ArrayFree(m_lastProcessedTickets);
}

//+------------------------------------------------------------------+
//| Send signal to backend                                           |
//+------------------------------------------------------------------+
bool CSignalManager::SendSignal(SignalData &signal) {
    if(m_httpClient == NULL) {
        m_logger.Error("HTTP client not initialized");
        return false;
    }
    
    // Validate signal
    if(m_validateSignals && !ValidateSignal(signal)) {
        m_logger.Warning("Signal validation failed");
        return false;
    }
    
    // Generate signal ID if not set
    if(StringLen(signal.signal_id) == 0) {
        signal.signal_id = GenerateSignalId();
    }
    
    // Set timestamp
    signal.timestamp = TimeCurrent();
    
    // Convert to JSON
    string jsonData = signal.ToJson();
    
    // Send to backend
    string endpoint = "/api/v1/signals";
    string response;
    int statusCode;
    
    m_logger.Debug(StringFormat("Sending signal: %s", jsonData));
    
    if(m_httpClient.Post(endpoint, jsonData, response, statusCode)) {
        if(statusCode == 200 || statusCode == 201) {
            m_logger.Info(StringFormat("Signal sent successfully. ID: %s", signal.signal_id));
            
            // Mark ticket as processed
            AddProcessedTicket(signal.ticket);
            m_lastSignalTime = TimeCurrent();
            
            return true;
        } else {
            m_logger.Error(StringFormat("Signal send failed. Status: %d, Response: %s", 
                                       statusCode, response));
        }
    } else {
        m_logger.Error("Failed to send HTTP request");
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| On tick processing                                               |
//+------------------------------------------------------------------+
void CSignalManager::OnTick() {
    // Cleanup old processed tickets periodically
    static datetime lastCleanup = 0;
    if(TimeCurrent() - lastCleanup > 3600) { // Every hour
        CleanupOldTickets();
        lastCleanup = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Process pending signals                                          |
//+------------------------------------------------------------------+
void CSignalManager::ProcessPendingSignals() {
    // Check for new positions
    CheckForNewPositions();
    
    // Check for closed positions
    CheckForClosedPositions();
    
    // Check for modified positions
    CheckForModifiedPositions();
}

//+------------------------------------------------------------------+
//| Check for new positions                                          |
//+------------------------------------------------------------------+
void CSignalManager::CheckForNewPositions() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionSelectByIndex(i)) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
            
            // Check if this is a new position
            if(openTime > m_lastSignalTime && !IsTicketProcessed(ticket)) {
                SignalData signal;
                if(CreateSignalFromPosition(signal)) {
                    SendSignal(signal);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check for closed positions                                       |
//+------------------------------------------------------------------+
void CSignalManager::CheckForClosedPositions() {
    // Check recent history for closed positions
    datetime from = m_lastSignalTime > 0 ? m_lastSignalTime : TimeCurrent() - 60;
    datetime to = TimeCurrent();
    
    if(HistorySelect(from, to)) {
        int total = HistoryDealsTotal();
        
        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(ticket, DEAL_ENTRY);
                
                if(entry == DEAL_ENTRY_OUT) {
                    ulong positionId = HistoryDealGetInteger(ticket, DEAL_POSITION_ID);
                    
                    if(IsTicketProcessed(positionId)) {
                        SignalData signal;
                        if(CreateCloseSignal(positionId, signal)) {
                            SendSignal(signal);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check for modified positions                                     |
//+------------------------------------------------------------------+
void CSignalManager::CheckForModifiedPositions() {
    // This would require tracking previous SL/TP values
    // Implementation depends on specific requirements
}

//+------------------------------------------------------------------+
//| Create signal from current position                             |
//+------------------------------------------------------------------+
bool CSignalManager::CreateSignalFromPosition(SignalData &signal) {
    signal.action = "open";
    signal.ticket = PositionGetInteger(POSITION_TICKET);
    signal.symbol = PositionGetString(POSITION_SYMBOL);
    signal.volume = PositionGetDouble(POSITION_VOLUME);
    signal.open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    signal.stop_loss = PositionGetDouble(POSITION_SL);
    signal.take_profit = PositionGetDouble(POSITION_TP);
    signal.comment = PositionGetString(POSITION_COMMENT);
    signal.current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
    signal.profit = PositionGetDouble(POSITION_PROFIT);
    
    // Determine position type
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    signal.type = (posType == POSITION_TYPE_BUY) ? "buy" : "sell";
    
    return true;
}

//+------------------------------------------------------------------+
//| Create close signal                                              |
//+------------------------------------------------------------------+
bool CSignalManager::CreateCloseSignal(ulong ticket, SignalData &signal) {
    // Get position info from history
    if(!HistorySelectByPosition(ticket)) {
        return false;
    }
    
    int deals = HistoryDealsTotal();
    if(deals < 2) return false;
    
    // Get the closing deal
    ulong closeDeal = HistoryDealGetTicket(deals - 1);
    
    signal.action = "close";
    signal.ticket = ticket;
    signal.symbol = HistoryDealGetString(closeDeal, DEAL_SYMBOL);
    signal.volume = HistoryDealGetDouble(closeDeal, DEAL_VOLUME);
    signal.current_price = HistoryDealGetDouble(closeDeal, DEAL_PRICE);
    signal.profit = HistoryDealGetDouble(closeDeal, DEAL_PROFIT);
    signal.comment = HistoryDealGetString(closeDeal, DEAL_COMMENT);
    
    return true;
}

//+------------------------------------------------------------------+
//| Create modify signal                                             |
//+------------------------------------------------------------------+
bool CSignalManager::CreateModifySignal(ulong ticket, SignalData &signal) {
    if(!PositionSelectByTicket(ticket)) {
        return false;
    }
    
    signal.action = "modify";
    signal.ticket = ticket;
    signal.symbol = PositionGetString(POSITION_SYMBOL);
    signal.stop_loss = PositionGetDouble(POSITION_SL);
    signal.take_profit = PositionGetDouble(POSITION_TP);
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate signal                                                  |
//+------------------------------------------------------------------+
bool CSignalManager::ValidateSignal(const SignalData &signal) {
    // Check volume range
    if(signal.volume < m_minVolume || signal.volume > m_maxVolume) {
        m_logger.Warning(StringFormat("Volume out of range: %.2f", signal.volume));
        return false;
    }
    
    // Check symbol
    if(StringLen(signal.symbol) == 0) {
        m_logger.Warning("Empty symbol");
        return false;
    }
    
    // Check action
    if(signal.action != "open" && signal.action != "close" && signal.action != "modify") {
        m_logger.Warning(StringFormat("Invalid action: %s", signal.action));
        return false;
    }
    
    // Check type for open signals
    if(signal.action == "open" && signal.type != "buy" && signal.type != "sell") {
        m_logger.Warning(StringFormat("Invalid type: %s", signal.type));
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Generate unique signal ID                                        |
//+------------------------------------------------------------------+
string CSignalManager::GenerateSignalId() {
    return StringFormat("SIG_%d_%d_%d", 
                       AccountInfoInteger(ACCOUNT_LOGIN),
                       TimeCurrent(),
                       MathRand());
}

//+------------------------------------------------------------------+
//| Add processed ticket                                             |
//+------------------------------------------------------------------+
void CSignalManager::AddProcessedTicket(ulong ticket) {
    int size = ArraySize(m_lastProcessedTickets);
    ArrayResize(m_lastProcessedTickets, size + 1);
    m_lastProcessedTickets[size] = ticket;
}

//+------------------------------------------------------------------+
//| Check if ticket is processed                                     |
//+------------------------------------------------------------------+
bool CSignalManager::IsTicketProcessed(ulong ticket) {
    for(int i = 0; i < ArraySize(m_lastProcessedTickets); i++) {
        if(m_lastProcessedTickets[i] == ticket) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Cleanup old tickets                                              |
//+------------------------------------------------------------------+
void CSignalManager::CleanupOldTickets() {
    // Keep only recent tickets (last 1000)
    int size = ArraySize(m_lastProcessedTickets);
    if(size > 1000) {
        int keepCount = 500;
        ulong tempArray[];
        ArrayResize(tempArray, keepCount);
        
        for(int i = 0; i < keepCount; i++) {
            tempArray[i] = m_lastProcessedTickets[size - keepCount + i];
        }
        
        ArrayCopy(m_lastProcessedTickets, tempArray);
        ArrayFree(tempArray);
        
        m_logger.Info(StringFormat("Cleaned up old tickets. Kept: %d", keepCount));
    }
}
