//+------------------------------------------------------------------+
//| HttpClient.mqh                                                   |
//| HTTP Client library for MT5 EA communication                    |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

//+------------------------------------------------------------------+
//| HTTP Client Class                                                |
//+------------------------------------------------------------------+
class CHttpClient {
private:
    string m_baseUrl;
    string m_apiKey;
    bool   m_enableSsl;
    int    m_timeout;
    string m_userAgent;
    
public:
    //--- Constructor
    CHttpClient(string baseUrl, string apiKey, bool enableSsl = true, int timeout = 5000);
    
    //--- Destructor
    ~CHttpClient();
    
    //--- HTTP Methods
    bool Get(string endpoint, string &response, int &statusCode);
    bool Post(string endpoint, string data, string &response, int &statusCode);
    bool Put(string endpoint, string data, string &response, int &statusCode);
    bool Delete(string endpoint, string &response, int &statusCode);
    
    //--- Configuration
    void SetTimeout(int timeout) { m_timeout = timeout; }
    void SetUserAgent(string userAgent) { m_userAgent = userAgent; }
    
private:
    //--- Helper methods
    string BuildUrl(string endpoint);
    string BuildHeaders(string contentType = "application/json");
    bool SendRequest(string method, string url, string headers, string data, string &response, int &statusCode);
    string UrlEncode(string str);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHttpClient::CHttpClient(string baseUrl, string apiKey, bool enableSsl = true, int timeout = 5000) {
    m_baseUrl = baseUrl;
    m_apiKey = apiKey;
    m_enableSsl = enableSsl;
    m_timeout = timeout;
    m_userAgent = "MT5-CopyTrade-EA/2.0";
    
    // Remove trailing slash from base URL
    if(StringSubstr(m_baseUrl, StringLen(m_baseUrl) - 1) == "/") {
        m_baseUrl = StringSubstr(m_baseUrl, 0, StringLen(m_baseUrl) - 1);
    }
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CHttpClient::~CHttpClient() {
    // Cleanup if needed
}

//+------------------------------------------------------------------+
//| GET Request                                                      |
//+------------------------------------------------------------------+
bool CHttpClient::Get(string endpoint, string &response, int &statusCode) {
    string url = BuildUrl(endpoint);
    string headers = BuildHeaders();
    
    return SendRequest("GET", url, headers, "", response, statusCode);
}

//+------------------------------------------------------------------+
//| POST Request                                                     |
//+------------------------------------------------------------------+
bool CHttpClient::Post(string endpoint, string data, string &response, int &statusCode) {
    string url = BuildUrl(endpoint);
    string headers = BuildHeaders("application/json");
    
    return SendRequest("POST", url, headers, data, response, statusCode);
}

//+------------------------------------------------------------------+
//| PUT Request                                                      |
//+------------------------------------------------------------------+
bool CHttpClient::Put(string endpoint, string data, string &response, int &statusCode) {
    string url = BuildUrl(endpoint);
    string headers = BuildHeaders("application/json");
    
    return SendRequest("PUT", url, headers, data, response, statusCode);
}

//+------------------------------------------------------------------+
//| DELETE Request                                                   |
//+------------------------------------------------------------------+
bool CHttpClient::Delete(string endpoint, string &response, int &statusCode) {
    string url = BuildUrl(endpoint);
    string headers = BuildHeaders();
    
    return SendRequest("DELETE", url, headers, "", response, statusCode);
}

//+------------------------------------------------------------------+
//| Build complete URL                                               |
//+------------------------------------------------------------------+
string CHttpClient::BuildUrl(string endpoint) {
    if(StringSubstr(endpoint, 0, 1) != "/") {
        endpoint = "/" + endpoint;
    }
    
    return m_baseUrl + endpoint;
}

//+------------------------------------------------------------------+
//| Build HTTP headers                                               |
//+------------------------------------------------------------------+
string CHttpClient::BuildHeaders(string contentType = "application/json") {
    string headers = "";
    
    // Authorization header
    if(StringLen(m_apiKey) > 0) {
        headers += "Authorization: Bearer " + m_apiKey + "\r\n";
    }
    
    // Content-Type header
    if(StringLen(contentType) > 0) {
        headers += "Content-Type: " + contentType + "\r\n";
    }
    
    // User-Agent header
    headers += "User-Agent: " + m_userAgent + "\r\n";
    
    // Accept header
    headers += "Accept: application/json\r\n";
    
    // Connection header
    headers += "Connection: close\r\n";
    
    return headers;
}

//+------------------------------------------------------------------+
//| Send HTTP request with retry logic                              |
//+------------------------------------------------------------------+
bool CHttpClient::SendRequest(string method, string url, string headers, string data, string &response, int &statusCode) {
    return SendRequestWithRetry(method, url, headers, data, response, statusCode, 3);
}

bool CHttpClient::SendRequestWithRetry(string method, string url, string headers, string data, string &response, int &statusCode, int maxRetries) {
    for(int attempt = 1; attempt <= maxRetries; attempt++) {
        if(SendSingleRequest(method, url, headers, data, response, statusCode)) {
            return true;
        }

        Print(StringFormat("Request attempt %d failed. Status: %d", attempt, statusCode));

        if(attempt < maxRetries) {
            Sleep(1000 * attempt); // Exponential backoff
        }
    }

    Print(StringFormat("Request failed after %d attempts", maxRetries));
    return false;
}

bool CHttpClient::SendSingleRequest(string method, string url, string headers, string data, string &response, int &statusCode) {
    char postData[];
    char resultData[];
    string resultHeaders;

    // Convert data to char array
    if(StringLen(data) > 0) {
        StringToCharArray(data, postData, 0, StringLen(data));
    }

    // Send request
    int result = WebRequest(method, url, headers, m_timeout, postData, resultData, resultHeaders);

    // Parse response
    if(result > 0) {
        statusCode = result;
        response = CharArrayToString(resultData);

        // Check for server errors that should be retried
        if(statusCode >= 500 && statusCode < 600) {
            Print(StringFormat("Server error %d, will retry", statusCode));
            return false;
        }

        return true;
    } else {
        statusCode = result;
        response = "";

        // Log error details
        int error = GetLastError();
        Print(StringFormat("WebRequest failed. Error: %d, URL: %s", error, url));

        // Check for network errors that should be retried
        if(error == 4060 || error == 4061 || error == 4062) { // Network errors
            return false;
        }

        return false;
    }
}

//+------------------------------------------------------------------+
//| Enhanced error handling                                          |
//+------------------------------------------------------------------+
string CHttpClient::GetErrorDescription(int errorCode) {
    switch(errorCode) {
        case 4060: return "No connection to server";
        case 4061: return "URL not found";
        case 4062: return "Request timeout";
        case 4014: return "Function not allowed in testing mode";
        case 5203: return "Object does not exist";
        default: return StringFormat("Unknown error: %d", errorCode);
    }
}

bool CHttpClient::IsRetryableError(int errorCode) {
    switch(errorCode) {
        case 4060: // No connection
        case 4062: // Timeout
        case -1:   // General network error
            return true;
        default:
            return false;
    }
}

//+------------------------------------------------------------------+
//| Connection health check                                          |
//+------------------------------------------------------------------+
bool CHttpClient::HealthCheck() {
    string response;
    int statusCode;

    string healthUrl = m_baseUrl + "/health";
    string headers = "User-Agent: " + m_userAgent + "\r\n";

    if(SendSingleRequest("GET", healthUrl, headers, "", response, statusCode)) {
        return statusCode == 200;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Request queue for offline scenarios                             |
//+------------------------------------------------------------------+
struct QueuedRequest {
    string method;
    string endpoint;
    string data;
    datetime timestamp;
    int attempts;
};

class CRequestQueue {
private:
    QueuedRequest m_queue[];
    int m_maxQueueSize;

public:
    CRequestQueue(int maxSize = 100) {
        m_maxQueueSize = maxSize;
        ArrayResize(m_queue, 0);
    }

    void AddRequest(string method, string endpoint, string data) {
        int size = ArraySize(m_queue);

        // Check queue size limit
        if(size >= m_maxQueueSize) {
            // Remove oldest request
            for(int i = 0; i < size - 1; i++) {
                m_queue[i] = m_queue[i + 1];
            }
            size--;
        }

        // Add new request
        ArrayResize(m_queue, size + 1);
        m_queue[size].method = method;
        m_queue[size].endpoint = endpoint;
        m_queue[size].data = data;
        m_queue[size].timestamp = TimeCurrent();
        m_queue[size].attempts = 0;
    }

    bool ProcessQueue(CHttpClient* httpClient) {
        bool anyProcessed = false;

        for(int i = ArraySize(m_queue) - 1; i >= 0; i--) {
            QueuedRequest req = m_queue[i];

            // Skip if too many attempts
            if(req.attempts >= 3) {
                RemoveRequest(i);
                continue;
            }

            // Skip if too old (1 hour)
            if(TimeCurrent() - req.timestamp > 3600) {
                RemoveRequest(i);
                continue;
            }

            // Try to send request
            string response;
            int statusCode;

            if(httpClient.SendSingleRequest(req.method, httpClient.BuildUrl(req.endpoint),
                                          httpClient.BuildHeaders(), req.data, response, statusCode)) {
                // Success - remove from queue
                RemoveRequest(i);
                anyProcessed = true;
            } else {
                // Failed - increment attempts
                m_queue[i].attempts++;
            }
        }

        return anyProcessed;
    }

    void RemoveRequest(int index) {
        int size = ArraySize(m_queue);

        for(int i = index; i < size - 1; i++) {
            m_queue[i] = m_queue[i + 1];
        }

        ArrayResize(m_queue, size - 1);
    }

    int GetQueueSize() {
        return ArraySize(m_queue);
    }

    void ClearQueue() {
        ArrayResize(m_queue, 0);
    }
};

//+------------------------------------------------------------------+
//| URL Encode string                                                |
//+------------------------------------------------------------------+
string CHttpClient::UrlEncode(string str) {
    string result = "";
    
    for(int i = 0; i < StringLen(str); i++) {
        ushort ch = StringGetCharacter(str, i);
        
        if((ch >= 'A' && ch <= 'Z') || 
           (ch >= 'a' && ch <= 'z') || 
           (ch >= '0' && ch <= '9') ||
           ch == '-' || ch == '_' || ch == '.' || ch == '~') {
            result += CharToString((char)ch);
        } else {
            result += StringFormat("%%%02X", ch);
        }
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| Signal Data Structure                                            |
//+------------------------------------------------------------------+
struct SignalData {
    string   signal_id;
    string   provider_id;
    string   action;        // "open", "close", "modify"
    ulong    ticket;
    string   symbol;
    string   type;          // "buy", "sell"
    double   volume;
    double   open_price;
    double   stop_loss;
    double   take_profit;
    string   comment;
    datetime timestamp;
    double   current_price;
    double   profit;
    
    // Constructor
    SignalData() {
        signal_id = "";
        provider_id = "";
        action = "";
        ticket = 0;
        symbol = "";
        type = "";
        volume = 0.0;
        open_price = 0.0;
        stop_loss = 0.0;
        take_profit = 0.0;
        comment = "";
        timestamp = 0;
        current_price = 0.0;
        profit = 0.0;
    }
    
    // Convert to JSON
    string ToJson() {
        return StringFormat(
            "{"
            "\"signal_id\":\"%s\","
            "\"provider_id\":\"%s\","
            "\"action\":\"%s\","
            "\"ticket\":%d,"
            "\"symbol\":\"%s\","
            "\"type\":\"%s\","
            "\"volume\":%.2f,"
            "\"open_price\":%.5f,"
            "\"stop_loss\":%.5f,"
            "\"take_profit\":%.5f,"
            "\"comment\":\"%s\","
            "\"timestamp\":%d,"
            "\"current_price\":%.5f,"
            "\"profit\":%.2f"
            "}",
            signal_id, provider_id, action, ticket, symbol, type,
            volume, open_price, stop_loss, take_profit, comment,
            timestamp, current_price, profit
        );
    }
};

//+------------------------------------------------------------------+
//| Execution Result Structure                                       |
//+------------------------------------------------------------------+
struct ExecutionResult {
    string   execution_id;
    string   signal_id;
    string   follower_id;
    bool     success;
    ulong    ticket;
    double   executed_volume;
    double   executed_price;
    string   error_message;
    datetime execution_time;
    
    // Constructor
    ExecutionResult() {
        execution_id = "";
        signal_id = "";
        follower_id = "";
        success = false;
        ticket = 0;
        executed_volume = 0.0;
        executed_price = 0.0;
        error_message = "";
        execution_time = 0;
    }
    
    // Convert to JSON
    string ToJson() {
        return StringFormat(
            "{"
            "\"execution_id\":\"%s\","
            "\"signal_id\":\"%s\","
            "\"follower_id\":\"%s\","
            "\"success\":%s,"
            "\"ticket\":%d,"
            "\"executed_volume\":%.2f,"
            "\"executed_price\":%.5f,"
            "\"error_message\":\"%s\","
            "\"execution_time\":%d"
            "}",
            execution_id, signal_id, follower_id,
            success ? "true" : "false",
            ticket, executed_volume, executed_price,
            error_message, execution_time
        );
    }
};
