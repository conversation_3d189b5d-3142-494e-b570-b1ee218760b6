//+------------------------------------------------------------------+
//| ConfigManager.mqh                                                |
//| Configuration Management for Provider EA                        |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "1.00"

#include "Logger.mqh"

//+------------------------------------------------------------------+
//| Configuration Manager Class                                      |
//+------------------------------------------------------------------+
class CConfigManager {
private:
    CLogger*    m_logger;
    string      m_configFile;
    
    // Configuration values
    string      m_apiBaseUrl;
    string      m_apiKey;
    string      m_providerId;
    bool        m_autoSendSignals;
    bool        m_sendPendingOrders;
    bool        m_validateSignals;
    int         m_maxDailySignals;
    double      m_maxSignalVolume;
    double      m_minVolume;
    int         m_heartbeatInterval;
    bool        m_enableLogging;
    int         m_logLevel;

public:
    // Constructor
    CConfigManager(CLogger* logger = NULL) {
        m_logger = logger;
        m_configFile = "ea_config.ini";
        
        // Default values
        m_apiBaseUrl = "http://localhost:8000";
        m_apiKey = "";
        m_providerId = "";
        m_autoSendSignals = true;
        m_sendPendingOrders = false;
        m_validateSignals = true;
        m_maxDailySignals = 50;
        m_maxSignalVolume = 10.0;
        m_minVolume = 0.01;
        m_heartbeatInterval = 30;
        m_enableLogging = true;
        m_logLevel = 2;
    }
    
    // Destructor
    ~CConfigManager() {
        // Cleanup if needed
    }
    
    // Load configuration from file
    bool LoadConfig() {
        if(m_logger != NULL) {
            m_logger.Info("Loading configuration from: " + m_configFile);
        }
        
        int fileHandle = FileOpen(m_configFile, FILE_READ | FILE_TXT);
        
        if(fileHandle == INVALID_HANDLE) {
            if(m_logger != NULL) {
                m_logger.Warning("Config file not found, using defaults");
            }
            return false;
        }
        
        string line;
        while(!FileIsEnding(fileHandle)) {
            line = FileReadString(fileHandle);
            ParseConfigLine(line);
        }
        
        FileClose(fileHandle);
        
        if(m_logger != NULL) {
            m_logger.Info("Configuration loaded successfully");
        }
        
        return true;
    }
    
    // Save configuration to file
    bool SaveConfig() {
        if(m_logger != NULL) {
            m_logger.Info("Saving configuration to: " + m_configFile);
        }
        
        int fileHandle = FileOpen(m_configFile, FILE_WRITE | FILE_TXT);
        
        if(fileHandle == INVALID_HANDLE) {
            if(m_logger != NULL) {
                m_logger.Error("Failed to create config file");
            }
            return false;
        }
        
        // Write configuration sections
        FileWrite(fileHandle, "[API]");
        FileWrite(fileHandle, "base_url=" + m_apiBaseUrl);
        FileWrite(fileHandle, "api_key=" + m_apiKey);
        FileWrite(fileHandle, "");
        
        FileWrite(fileHandle, "[PROVIDER]");
        FileWrite(fileHandle, "provider_id=" + m_providerId);
        FileWrite(fileHandle, "auto_send_signals=" + (m_autoSendSignals ? "true" : "false"));
        FileWrite(fileHandle, "send_pending_orders=" + (m_sendPendingOrders ? "true" : "false"));
        FileWrite(fileHandle, "validate_signals=" + (m_validateSignals ? "true" : "false"));
        FileWrite(fileHandle, "max_daily_signals=" + IntegerToString(m_maxDailySignals));
        FileWrite(fileHandle, "max_signal_volume=" + DoubleToString(m_maxSignalVolume, 2));
        FileWrite(fileHandle, "min_volume=" + DoubleToString(m_minVolume, 2));
        FileWrite(fileHandle, "");
        
        FileWrite(fileHandle, "[SYSTEM]");
        FileWrite(fileHandle, "heartbeat_interval=" + IntegerToString(m_heartbeatInterval));
        FileWrite(fileHandle, "enable_logging=" + (m_enableLogging ? "true" : "false"));
        FileWrite(fileHandle, "log_level=" + IntegerToString(m_logLevel));
        
        FileClose(fileHandle);
        
        if(m_logger != NULL) {
            m_logger.Info("Configuration saved successfully");
        }
        
        return true;
    }
    
    // Getters
    string GetApiBaseUrl() { return m_apiBaseUrl; }
    string GetApiKey() { return m_apiKey; }
    string GetProviderId() { return m_providerId; }
    bool GetAutoSendSignals() { return m_autoSendSignals; }
    bool GetSendPendingOrders() { return m_sendPendingOrders; }
    bool GetValidateSignals() { return m_validateSignals; }
    int GetMaxDailySignals() { return m_maxDailySignals; }
    double GetMaxSignalVolume() { return m_maxSignalVolume; }
    double GetMinVolume() { return m_minVolume; }
    int GetHeartbeatInterval() { return m_heartbeatInterval; }
    bool GetEnableLogging() { return m_enableLogging; }
    int GetLogLevel() { return m_logLevel; }
    
    // Setters
    void SetApiBaseUrl(string url) { m_apiBaseUrl = url; }
    void SetApiKey(string key) { m_apiKey = key; }
    void SetProviderId(string id) { m_providerId = id; }
    void SetAutoSendSignals(bool enable) { m_autoSendSignals = enable; }
    void SetSendPendingOrders(bool enable) { m_sendPendingOrders = enable; }
    void SetValidateSignals(bool enable) { m_validateSignals = enable; }
    void SetMaxDailySignals(int max) { m_maxDailySignals = max; }
    void SetMaxSignalVolume(double max) { m_maxSignalVolume = max; }
    void SetMinVolume(double min) { m_minVolume = min; }
    void SetHeartbeatInterval(int interval) { m_heartbeatInterval = interval; }
    void SetEnableLogging(bool enable) { m_enableLogging = enable; }
    void SetLogLevel(int level) { m_logLevel = level; }
    
    // Update configuration from input parameters
    void UpdateFromInputs(string apiUrl, string apiKey, string providerId,
                         bool autoSend, bool sendPending, bool validate,
                         int maxDaily, double maxVolume, double minVol,
                         int heartbeat, bool logging, int logLvl) {
        m_apiBaseUrl = apiUrl;
        m_apiKey = apiKey;
        m_providerId = providerId;
        m_autoSendSignals = autoSend;
        m_sendPendingOrders = sendPending;
        m_validateSignals = validate;
        m_maxDailySignals = maxDaily;
        m_maxSignalVolume = maxVolume;
        m_minVolume = minVol;
        m_heartbeatInterval = heartbeat;
        m_enableLogging = logging;
        m_logLevel = logLvl;
    }
    
    // Validate configuration
    bool ValidateConfig() {
        bool isValid = true;
        
        if(StringLen(m_apiBaseUrl) == 0) {
            if(m_logger != NULL) {
                m_logger.Error("API Base URL is required");
            }
            isValid = false;
        }
        
        if(StringLen(m_apiKey) == 0) {
            if(m_logger != NULL) {
                m_logger.Error("API Key is required");
            }
            isValid = false;
        }
        
        if(StringLen(m_providerId) == 0) {
            if(m_logger != NULL) {
                m_logger.Error("Provider ID is required");
            }
            isValid = false;
        }
        
        if(m_maxDailySignals <= 0) {
            if(m_logger != NULL) {
                m_logger.Error("Max daily signals must be positive");
            }
            isValid = false;
        }
        
        if(m_maxSignalVolume <= 0) {
            if(m_logger != NULL) {
                m_logger.Error("Max signal volume must be positive");
            }
            isValid = false;
        }
        
        if(m_minVolume <= 0) {
            if(m_logger != NULL) {
                m_logger.Error("Min volume must be positive");
            }
            isValid = false;
        }
        
        if(m_heartbeatInterval <= 0) {
            if(m_logger != NULL) {
                m_logger.Error("Heartbeat interval must be positive");
            }
            isValid = false;
        }
        
        return isValid;
    }
    
    // Print current configuration
    void PrintConfig() {
        if(m_logger != NULL) {
            m_logger.Info("=== Current Configuration ===");
            m_logger.Info("API Base URL: " + m_apiBaseUrl);
            m_logger.Info("Provider ID: " + m_providerId);
            m_logger.Info("Auto Send Signals: " + (m_autoSendSignals ? "Yes" : "No"));
            m_logger.Info("Send Pending Orders: " + (m_sendPendingOrders ? "Yes" : "No"));
            m_logger.Info("Validate Signals: " + (m_validateSignals ? "Yes" : "No"));
            m_logger.Info("Max Daily Signals: " + IntegerToString(m_maxDailySignals));
            m_logger.Info("Max Signal Volume: " + DoubleToString(m_maxSignalVolume, 2));
            m_logger.Info("Min Volume: " + DoubleToString(m_minVolume, 2));
            m_logger.Info("Heartbeat Interval: " + IntegerToString(m_heartbeatInterval) + "s");
            m_logger.Info("Enable Logging: " + (m_enableLogging ? "Yes" : "No"));
            m_logger.Info("Log Level: " + IntegerToString(m_logLevel));
            m_logger.Info("=============================");
        }
    }

private:
    // Parse a single configuration line
    void ParseConfigLine(string line) {
        // Skip empty lines and comments
        if(StringLen(line) == 0 || StringGetCharacter(line, 0) == '#' || 
           StringGetCharacter(line, 0) == ';' || StringGetCharacter(line, 0) == '[') {
            return;
        }
        
        // Find the equals sign
        int equalPos = StringFind(line, "=");
        if(equalPos <= 0) {
            return;
        }
        
        string key = StringSubstr(line, 0, equalPos);
        string value = StringSubstr(line, equalPos + 1);
        
        // Trim whitespace
        StringTrimLeft(key);
        StringTrimRight(key);
        StringTrimLeft(value);
        StringTrimRight(value);
        
        // Parse based on key
        if(key == "base_url") {
            m_apiBaseUrl = value;
        } else if(key == "api_key") {
            m_apiKey = value;
        } else if(key == "provider_id") {
            m_providerId = value;
        } else if(key == "auto_send_signals") {
            m_autoSendSignals = (value == "true" || value == "1");
        } else if(key == "send_pending_orders") {
            m_sendPendingOrders = (value == "true" || value == "1");
        } else if(key == "validate_signals") {
            m_validateSignals = (value == "true" || value == "1");
        } else if(key == "max_daily_signals") {
            m_maxDailySignals = (int)StringToInteger(value);
        } else if(key == "max_signal_volume") {
            m_maxSignalVolume = StringToDouble(value);
        } else if(key == "min_volume") {
            m_minVolume = StringToDouble(value);
        } else if(key == "heartbeat_interval") {
            m_heartbeatInterval = (int)StringToInteger(value);
        } else if(key == "enable_logging") {
            m_enableLogging = (value == "true" || value == "1");
        } else if(key == "log_level") {
            m_logLevel = (int)StringToInteger(value);
        }
    }
};
